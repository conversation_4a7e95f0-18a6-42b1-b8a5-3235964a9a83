// Financial Data Flattener for Frontend Binding
// This utility transforms nested financial data into flat structures

export const flattenFinancialData = (data) => {
  console.log(data.wagesRevenue);
  let result = {
    totalIncome: parseFloat(data.ytdSummary[0].YTD_Total_Income),
    totalCOGS: parseFloat(data.ytdSummary[0].YTD_Total_COGS),
    totalExpense: parseFloat(data.ytdSummary[0].YTD_Total_Expense),
    netProfit: parseFloat(data.ytdSummary[0].YTD_Net_Profit),
    expensesTopAccounts: data.topExpensesBreakdown,
    expensesTopAccountsMonthly: data.expensesTopAccountsMonthly,
    daysSalesOutstanding: data.daysSalesOutstanding,
    daysPayablesOutstanding: data.daysPayablesOutstanding,
    netChangeInCash: data.netChangeInCash,
    fixedAssetTurnover: data.fixedAssetTurnover,
    // quickRatio: data.quickRatio,
    topExpensesMonthWise: data.topExpensesMonthWise,
    profitAndLossMonthsTrailing: data.profitAndLossMonthsTrailing,
    profitAndLossMonthsYTD: data.profitAndLossMonthsYTD,
    profitAndLossMonthly: data.profitAndLossMonthly,
    balanceSheetTableData: data.balanceSheetTableData,
    wagesRevenueMonthWise: data.wagesRevenueMonthWise,
  };

  let monthlyPerformanceBreakDown = [];
  let netIncomeLoss = [];
  let nerProfitMargin = [];
  let monthlyGrossProfitMargin = [];
  let roa = [],
    roe = [],
    dso = [],
    dpo = [],
    dio = [],
    CCC = [],
    monthOnCash = [],
    quickRatio = [];

    data.quickRatio.map((item) => {
      quickRatio.push({
        year: parseInt(item.year),
        month: parseInt(item.month),
        cashAndEquivalents: parseFloat(item.cash_and_equivalents),
        accountsReceivable: parseFloat(item.accounts_receivable),
        shortTermInvestments: parseFloat(item.short_term_investments),
        quickAssets: parseFloat(item.quick_assets),
        currentLiabilities: parseFloat(item.current_liabilities),
        quickRatio: parseFloat(item.quick_ratio),
      });
    });
    
  data.monthlyFinancialSummary.map((item) => {
    monthlyPerformanceBreakDown.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      totalIncome: parseFloat(item.total_income),
      totalCOGS: parseFloat(item.total_cogs),
      totalExpenses: parseFloat(item.total_expenses),
    });

    netIncomeLoss.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      netIncomeLoss: parseFloat(item.net_profit),
    });
    nerProfitMargin.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      nerProfitMargin: parseFloat(item.net_profit_margin_percentage),
    });
  });
  data.monthlyProfitLoss.map((item) => {
    monthlyGrossProfitMargin.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      Gross_Profit_Margin: parseFloat(item.Gross_Profit_Margin),
    });
  });

  data.roaAndRoe.map((item) => {
    roa.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      roa: parseFloat(item.roa),
      roe: parseFloat(item.roe),
    });
  });


  data.daysInventoryOutstanding.map((item) => {
    dio.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      dio: parseFloat(item.days_inventory_outstanding),
    });
  });

  data.cashConversionCycle.map((item) => {
    dso.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      dso: parseFloat(item.dso),
    });
    dpo.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      dpo: parseFloat(item.dpo),
    });
    CCC.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      CCC: parseFloat(item.ccc || 0),
    });
  });

  data.monthsOfCash.map((item) => {
    monthOnCash.push({
      year: parseInt(item.year),
      month: parseInt(item.month),
      monthsOfCash: parseFloat(item.months_cash),
    });
  });

  return (result = {
    ...result,
    monthlyPerformanceBreakDown: monthlyPerformanceBreakDown,
    netIncomeLoss: netIncomeLoss,
    nerProfitMargin: nerProfitMargin,
    monthlyGrossProfitMargin: monthlyGrossProfitMargin,
    roeRoa: roa,
    returnOnEquity: roe,
    daysSalesAROutstanding: dso,
    daysSalesAPOutstanding: dpo,
    daysInventoryOutstanding: dio,
    cashConversionCycle: CCC,
    monthOnCash,
    quickRatio
  });
};
