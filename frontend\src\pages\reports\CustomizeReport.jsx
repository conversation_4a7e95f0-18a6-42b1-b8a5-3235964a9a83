import React, { useState, useEffect } from 'react';
import { IconButton, Tooltip, CircularProgress, Box, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button } from '@mui/material';
import { Sync as SyncIcon, ArrowBack, Download as DownloadIcon } from '@mui/icons-material';
import { Snackbar, Alert } from '@mui/material';
import { useSearchParams } from 'react-router-dom';
import TableOfContents from './ReportPages/TableOfContents';
import ReportSummary from './ReportPages/ReportSummary';
import FiscalYearDashboard from './ReportPages/FiscalYear';
import ExpenseSummaryDashboard from './ReportPages/ExpenseSummary';
import OperationalEfficiencyDashboard from './ReportPages/OperationalEfficiency';
import LiquiditySummaryDashboard from './ReportPages/LiquiditySummary';
import ProfitLoss13MonthDashboard from './ReportPages/MonthTrailing';
import ProfitLossMonthlyDashboard from './ReportPages/Monthly';
import ProfitLossYTDDashboard from './ReportPages/YearToDate';
import BalanceSheetDashboard from './ReportPages/BalanceSheet';
import { useNavigate, useParams } from 'react-router-dom';
import axiosInstance from '../../services/axiosInstance';
import { downloadPDFFromBase64 } from '../../services/pdf';
import DeepSightCoverPage from './ReportPages/CoverPage';



const CustomizeTemplateWithPreview = () => {
  const [searchParams] = useSearchParams();

  const params = useParams();
  const companyId = params.id;

  // API Data State
  const [reportData, setReportData] = useState(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [dataError, setDataError] = useState(null);

  // Template Settings State
  const [templateSettings, setTemplateSettings] = useState(null);
  const [initialSettings, setInitialSettings] = useState(null); // Store initial settings for reset
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [settingsError, setSettingsError] = useState(null);

  // Content Settings State (NEW)
  const [contentSettings, setContentSettings] = useState(null);
  const [isLoadingContentSettings, setIsLoadingContentSettings] = useState(true);
  const [contentSettingsError, setContentSettingsError] = useState(null);

  // UI State
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('Deepsight');
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [isSavingSettings, setIsSavingSettings] = useState(false);

  // Modal State
  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);
  const [showResetConfirmModal, setShowResetConfirmModal] = useState(false);

  const navigate = useNavigate();

  //fetching company id for report data

  // Default settings fallback
  const defaultSettings = {
    header: {
      fontStyle: 'Helvetica',
      fontType: 'Bold',
      fontSize: 44,
      color: '#1e7c8c'
    },
    heading: {
      fontStyle: 'Helvetica',
      fontType: 'Bold',
      fontSize: 36,
      color: '#1e7c8c'
    },
    subHeading: {
      fontStyle: 'Helvetica',
      fontType: 'Bold',
      fontSize: 22,
      color: '#1e7c8c'
    },
    content: {
      fontStyle: 'Helvetica',
      fontType: 'Regular',
      fontSize: 15,
      color: '#333333'
    }
  };

  const fontStyles = ["Open Sans",'Calibri', 'Arial', 'Times New Roman', 'Georgia', 'Verdana', 'Helvetica'];
  const fontTypes = ['Regular', 'Bold'];

  // Font size constraints
  const fontSizeConstraints = {
    header: { min: 32, max: 48 },
    heading: { min: 32, max: 42 },
    subHeading: { min: 18, max: 28 },
    content: { min: 9, max: 20 }
  };

  // Fetch content settings from API (NEW)
  const fetchContentSettings = async () => {
    try {
      setIsLoadingContentSettings(true);
      setContentSettingsError(null);

      const response = await axiosInstance.get(`/content-settings/company/${companyId}/DEEPSIGHT`);

      if (response.data && response.data.success) {
        const settingsData = response.data.data;
        console.log('Content settings loaded from API:', settingsData);
        setContentSettings(settingsData);
      } else {
        throw new Error('Failed to fetch content settings');
      }
    } catch (error) {
      console.error('Error fetching content settings:', error);
      setContentSettingsError(error.message);

      // Set default content settings if API fails
      const defaultContentSettings = {
        chartSettings: {
          incomeSummary: true,
          netIncome: true,
          grossProfitMargin: true,
          netProfitMargin: true,
          roaAndRoe: true,
        }
      };
      setContentSettings(defaultContentSettings);

      setSuccessMessage('Using default chart settings. Failed to load from server.');
      setShowSuccess(true);
    } finally {
      setIsLoadingContentSettings(false);
    }
  };

  // Fetch template settings from API
  const fetchTemplateSettings = async () => {
    try {
      setIsLoadingSettings(true);
      setSettingsError(null);

      const response = await axiosInstance.get('/template-settings');

      if (response.data && response.data.success) {
        const settingsData = response.data.data.settings;

        // Store settings in localStorage
        localStorage.setItem('templateSettings', JSON.stringify(settingsData));

        // Set both current and initial settings
        setTemplateSettings(settingsData);
        setInitialSettings(settingsData);

        console.log('Template settings loaded from API:', response.data.data);
      } else {
        throw new Error('Failed to fetch template settings');
      }
    } catch (error) {
      console.error('Error fetching template settings:', error);
      setSettingsError(error.message);

      // Fallback to localStorage or default settings
      const savedSettings = localStorage.getItem('templateSettings');
      const fallbackSettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;

      setTemplateSettings(fallbackSettings);
      setInitialSettings(fallbackSettings);

      setSuccessMessage('Using cached settings. Failed to load from server.');
      setShowSuccess(true);
    } finally {
      setIsLoadingSettings(false);
    }
  };

  // Load settings from URL params (for PDF generation) or fetch from API
  const initializeSettings = () => {
    try {
      // First check if settings are in URL (for PDF generation)
      const urlSettings = searchParams.get('templateSettings');
      if (urlSettings) {
        const parsedSettings = JSON.parse(urlSettings);
        setTemplateSettings(parsedSettings);
        setInitialSettings(parsedSettings);
        setIsLoadingSettings(false);
        return;
      }

      // Otherwise fetch from API
      fetchTemplateSettings();
    } catch (error) {
      console.error('Error initializing settings:', error);
      setTemplateSettings(defaultSettings);
      setInitialSettings(defaultSettings);
      setIsLoadingSettings(false);
    }
  };

 // Update the fetchReportData function to use the companyId from params
const fetchReportData = async () => {
  try {
    setIsLoadingData(true);
    setDataError(null);

    // Change this line - use companyId from params instead of hardcoded 1
    const response = await axiosInstance.get(`/report/${companyId}/generate-calculation`);

    if (response.data && response.data.success) {
      console.log('CustomizeReport - API Response:', response.data.data);
      setReportData(response.data.data);
    } else {
      throw new Error('Failed to fetch report data');
    }
  } catch (error) {
    console.error('Error fetching report data:', error);
    setDataError(error.message);
    setSuccessMessage('Failed to load report data. Please try again.');
    setShowSuccess(true);
  } finally {
    setIsLoadingData(false);
  }
};
  // Generate and download PDF
// In CustomizeReport.jsx, update the handleDownloadPDF function

// In CustomizeReport.jsx, update the handleDownloadPDF function

const handleDownloadPDF = async () => {
  try {
    setIsGeneratingPDF(true);

    // Get the scrollable content panel that contains all components
    const contentPanel = document.querySelector('.flex-1.overflow-y-auto.bg-gray-200');
    if (!contentPanel) {
      throw new Error('Could not find report content to generate PDF');
    }

    // Wait for charts to fully render before capturing content
    await new Promise(resolve => setTimeout(resolve, 3000));

    // FREEZE CHART ELEMENTS AND LEGENDS BEFORE CLONING
    const freezeChartsAndLegends = (container) => {
      // 1. Freeze Chart Elements
      const chartContainers = container.querySelectorAll('div[id*="apex"]');
      console.log(`Found ${chartContainers.length} charts to freeze`);
      
      chartContainers.forEach((chartContainer, index) => {
        try {
          // Freeze all SVG elements within the chart
          const svgElements = chartContainer.querySelectorAll('svg, svg *');
          svgElements.forEach(svgEl => {
            // Get computed styles and apply them inline
            const computedStyle = window.getComputedStyle(svgEl);
            
            // Apply critical styles inline to prevent changes
            if (svgEl.tagName.toLowerCase() === 'svg') {
              svgEl.style.width = computedStyle.width;
              svgEl.style.height = computedStyle.height;
              svgEl.style.overflow = 'visible';
            }
            
            // Freeze stroke and fill properties
            if (computedStyle.stroke && computedStyle.stroke !== 'none') {
              svgEl.style.stroke = computedStyle.stroke;
            }
            if (computedStyle.strokeWidth && computedStyle.strokeWidth !== '0px') {
              svgEl.style.strokeWidth = computedStyle.strokeWidth;
            }
            if (computedStyle.fill && computedStyle.fill !== 'none') {
              svgEl.style.fill = computedStyle.fill;
            }
            if (computedStyle.strokeDasharray && computedStyle.strokeDasharray !== 'none') {
              svgEl.style.strokeDasharray = computedStyle.strokeDasharray;
            }
            
            // Prevent any transformations from changing
            if (computedStyle.transform && computedStyle.transform !== 'none') {
              svgEl.style.transform = computedStyle.transform;
            }
          });
          
          // Freeze the chart container dimensions and position
          const containerRect = chartContainer.getBoundingClientRect();
          chartContainer.style.width = containerRect.width + 'px';
          chartContainer.style.height = containerRect.height + 'px';
          chartContainer.style.position = 'relative';
          chartContainer.style.overflow = 'visible';
          
          // Freeze all ApexCharts specific elements
          const apexElements = chartContainer.querySelectorAll('[class*="apexcharts"]');
          apexElements.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            
            // Preserve positioning
            if (computedStyle.position && computedStyle.position !== 'static') {
              element.style.position = computedStyle.position;
              element.style.top = computedStyle.top;
              element.style.left = computedStyle.left;
              element.style.right = computedStyle.right;
              element.style.bottom = computedStyle.bottom;
            }
            
            // Preserve dimensions
            element.style.width = computedStyle.width;
            element.style.height = computedStyle.height;
            
            // Preserve display and visibility
            element.style.display = computedStyle.display;
            element.style.visibility = computedStyle.visibility;
            element.style.opacity = computedStyle.opacity;
          });
          
          // Specifically handle grid lines and axis elements that might be causing extra lines
          const gridLines = chartContainer.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');
          gridLines.forEach(line => {
            const computedStyle = window.getComputedStyle(line);
            line.style.stroke = computedStyle.stroke;
            line.style.strokeWidth = computedStyle.strokeWidth;
            line.style.strokeDasharray = computedStyle.strokeDasharray;
            line.style.opacity = computedStyle.opacity;
          });
          
          // Handle axis lines
          const axisLines = chartContainer.querySelectorAll('.apexcharts-xaxis line, .apexcharts-yaxis line');
          axisLines.forEach(line => {
            const computedStyle = window.getComputedStyle(line);
            line.style.stroke = computedStyle.stroke;
            line.style.strokeWidth = computedStyle.strokeWidth;
            line.style.opacity = computedStyle.opacity;
          });
          
          // Handle data series paths/bars
          const seriesElements = chartContainer.querySelectorAll('.apexcharts-series path, .apexcharts-series rect, .apexcharts-series circle');
          seriesElements.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            if (computedStyle.fill) element.style.fill = computedStyle.fill;
            if (computedStyle.stroke) element.style.stroke = computedStyle.stroke;
            if (computedStyle.strokeWidth) element.style.strokeWidth = computedStyle.strokeWidth;
            if (computedStyle.opacity) element.style.opacity = computedStyle.opacity;
          });
          
          console.log(`Chart ${index} frozen with dimensions: ${containerRect.width}x${containerRect.height}`);
          
        } catch (error) {
          console.error(`Error freezing chart ${index}:`, error);
        }
      });
      
      // 2. Freeze Legend Elements (your existing legend freezing code)
      const legends = container.querySelectorAll('.apexcharts-legend');
      console.log(`Found ${legends.length} legends to freeze`);
      
      legends.forEach((legend, index) => {
        try {
          // Get current position relative to viewport
          const rect = legend.getBoundingClientRect();
          
          // Find the closest chart container or parent container
          let chartContainer = legend.closest('div[id*="apex"]');
          if (!chartContainer) {
            // Look for chart section container
            chartContainer = legend.closest('.bg-white.p-6');
          }
          if (!chartContainer) {
            // Fallback to component container
            chartContainer = legend.closest('.min-h-screen');
          }

          if (chartContainer) {
            const parentRect = chartContainer.getBoundingClientRect();
            
            // Calculate position relative to the chart container
            const relativeTop = rect.top - parentRect.top;
            const relativeLeft = rect.left - parentRect.left;
            
            // Store original styles for debugging
            legend.setAttribute('data-original-position', legend.style.position || '');
            legend.setAttribute('data-original-top', legend.style.top || '');
            legend.setAttribute('data-original-left', legend.style.left || '');
            
            // Set the legend to absolute positioning relative to its chart container
            legend.style.position = 'absolute';
            legend.style.top = Math.max(0, relativeTop) + 'px';
            legend.style.left = relativeLeft + 'px';
            legend.style.right = 'auto';
            legend.style.bottom = 'auto';
            legend.style.transform = 'none';
            legend.style.zIndex = '10';
            
            // Ensure the chart container has relative positioning
            const containerPosition = window.getComputedStyle(chartContainer).position;
            if (containerPosition === 'static') {
              chartContainer.style.position = 'relative';
            }
            
            // Freeze legend's internal styling
            const legendElements = legend.querySelectorAll('*');
            legendElements.forEach(el => {
              const computedStyle = window.getComputedStyle(el);
              if (computedStyle.color) el.style.color = computedStyle.color;
              if (computedStyle.fontSize) el.style.fontSize = computedStyle.fontSize;
              if (computedStyle.fontFamily) el.style.fontFamily = computedStyle.fontFamily;
              if (computedStyle.fontWeight) el.style.fontWeight = computedStyle.fontWeight;
            });
            
            console.log(`Legend ${index} frozen at position: ${relativeTop}px, ${relativeLeft}px`);
            
          } else {
            console.warn(`Could not find chart container for legend ${index}`);
            
            // Fallback: just prevent the legend from moving by setting fixed position
            legend.style.position = 'relative';
            legend.style.top = '20px';
            legend.style.left = 'auto';
            legend.style.right = 'auto';
            legend.style.transform = 'none';
            legend.style.margin = '20px auto';
            legend.style.display = 'flex';
            legend.style.justifyContent = 'center';
          }
          
        } catch (error) {
          console.error(`Error freezing legend ${index}:`, error);
          
          // Emergency fallback - just make it static
          legend.style.position = 'static';
          legend.style.margin = '20px auto';
          legend.style.display = 'flex';
          legend.style.justifyContent = 'center';
        }
      });
    };

    // Apply the freezing function
    freezeChartsAndLegends(contentPanel);

    // Wait a bit more for positions to settle after freezing
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Now clone the content with frozen chart and legend positions
    const clonedContent = contentPanel.cloneNode(true);

    // Additional safety check: ensure no extra lines are added during cloning
    const clonedCharts = clonedContent.querySelectorAll('div[id*="apex"]');
    clonedCharts.forEach((chart, index) => {
      // Remove any duplicate or extra grid lines that might have been created
      const gridLines = chart.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');
      const seenLines = new Set();
      
      gridLines.forEach(line => {
        const lineKey = `${line.getAttribute('x1')}-${line.getAttribute('y1')}-${line.getAttribute('x2')}-${line.getAttribute('y2')}`;
        if (seenLines.has(lineKey)) {
          // Duplicate line, remove it
          line.remove();
        } else {
          seenLines.add(lineKey);
        }
      });
      
      // Ensure chart maintains its frozen dimensions
      chart.style.minHeight = 'auto';
      chart.style.maxHeight = 'none';
    });

    // Additional safety check: fix any legends that might still be mispositioned in cloned content
    const clonedLegends = clonedContent.querySelectorAll('.apexcharts-legend');
    clonedLegends.forEach((legend, index) => {
      // If legend still has a very high top value, reset it
      const topValue = parseFloat(legend.style.top) || 0;
      if (topValue > 500) {
        console.log(`Fixing mispositioned cloned legend ${index} with top: ${topValue}px`);
        legend.style.top = '20px';
        legend.style.position = 'relative';
        legend.style.margin = '20px auto 10px auto';
        legend.style.display = 'flex';
        legend.style.justifyContent = 'center';
      }
    });

    // Find all components and modify them for proper header repetition
    const components = clonedContent.querySelectorAll('.min-h-screen');
    components.forEach((component) => {
      const componentHeader = component.querySelector('.component-header');
      const reportHeader = component.querySelector('.report-header');
      const metricGrid = component.querySelector(".metrics-flex");

      let header = componentHeader || reportHeader;

      // Check if this is the MonthTrailing component by looking for the unique max-w-8xl class
      // Also check for the specific text content as a fallback, but only if it has the right structure
      const hasMaxW8xl = component.querySelector('.max-w-8xl') !== null;
      const hasTrailingText = component.textContent.includes('13 Month Trailing');
      const hasProfitLossTable = component.querySelector('.profit-loss-table') !== null;

      const isMonthTrailing = hasMaxW8xl || (hasTrailingText && hasProfitLossTable);

      // Debug logging
      if (hasTrailingText) {
        console.log('Found component with 13 Month Trailing text');
        console.log('Component classes:', component.className);
        console.log('Has max-w-8xl:', hasMaxW8xl);
        console.log('Has profit-loss-table:', hasProfitLossTable);
        console.log('Final isMonthTrailing:', isMonthTrailing);
      }

      // Identify component types for disclaimer footer
      const isMonthly = component.querySelector('.max-w-6xl') &&
                       component.classList.contains('profit-loss-section') &&
                       component.textContent.includes('Monthly Report');

      const isYearToDate = component.querySelector('.max-w-6xl') &&
                          component.classList.contains('profit-loss-section') &&
                          component.textContent.includes('Year to Date');

      const isBalanceSheet = component.textContent.includes('Balance Sheet');

      // Check if this component should have repeating headers
      // MonthTrailing (max-w-8xl), Monthly (max-w-6xl + profit-loss-section), YearToDate (max-w-6xl + profit-loss-section), BalanceSheet
      const shouldHaveRepeatingHeaders = isMonthTrailing ||
                                       (component.querySelector('.max-w-6xl') && component.classList.contains('profit-loss-section')) ||
                                       component.textContent.includes('Balance Sheet');

      // Add data attributes for components that need disclaimer footer
      if (isMonthTrailing) {
        component.setAttribute('data-component', 'month-trailing');
        component.setAttribute('data-needs-disclaimer-footer', 'true');
      } else if (isMonthly) {
        component.setAttribute('data-component', 'monthly');
        component.setAttribute('data-needs-disclaimer-footer', 'true');
      } else if (isYearToDate) {
        component.setAttribute('data-component', 'year-to-date');
        component.setAttribute('data-needs-disclaimer-footer', 'true');
      } else if (isBalanceSheet) {
        component.setAttribute('data-component', 'balance-sheet');
        component.setAttribute('data-needs-disclaimer-footer', 'true');
      }

      if (header) {
        if (!header.classList.contains('component-header')) {
          header.classList.add('component-header');
        }

        // Add special class for MonthTrailing component
        if (isMonthTrailing) {
          console.log('Adding month-trailing-component class to component');
          component.classList.add('month-trailing-component');
          header.classList.add('month-trailing-header');

          // Also add inline styles to ensure page orientation is applied
          component.style.page = 'month-trailing';
          component.style.pageBreakBefore = 'always';

          // Try setting the page size directly on the component
          component.style.setProperty('page', 'month-trailing', 'important');
        }

        // Add class for components that should have repeating headers
        if (shouldHaveRepeatingHeaders) {
          header.classList.add('repeating-header');

          // Ensure the header structure is correct for repetition
          header.style.display = 'table-header-group';
          header.style.pageBreakInside = 'avoid';
          header.style.pageBreakAfter = 'avoid';
        }

        const allChildren = Array.from(component.children);
        let nonHeaderChildren;

        if (componentHeader) {
          nonHeaderChildren = allChildren.filter(child =>
            !child.classList.contains('component-header')
          );
        } else if (reportHeader) {
          nonHeaderChildren = allChildren.filter(child =>
            !child.classList.contains('report-header')
          );
        }

        if (nonHeaderChildren && nonHeaderChildren.length > 0) {
          const contentGroup = document.createElement('div');
          contentGroup.className = 'component-content';

          nonHeaderChildren.forEach(child => {
            contentGroup.appendChild(child);
          });

          component.appendChild(contentGroup);
        }

        component.style.display = 'table';
        component.style.width = '100%';
      }

      // Ensure table headers repeat for components with repeating headers
      if (shouldHaveRepeatingHeaders) {
        const tables = component.querySelectorAll('table');
        tables.forEach(table => {
          const thead = table.querySelector('thead');
          if (thead) {
            thead.style.display = 'table-header-group';
            thead.style.pageBreakInside = 'avoid';

            // Add specific classes for better control
            thead.classList.add('repeating-table-header');

            // Ensure table structure is correct for header repetition
            table.style.borderCollapse = 'collapse';
          }
        });
      }
    });

    // Create HTML with minimal CSS changes
    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Custom Report</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
        <style>
          @page {
            size: A4 portrait;
            margin-top: 0in;
          }

          /* Special page settings for MonthTrailing component */
          @page month-trailing {
            size: letter landscape;
          
          }

          /* Alternative approach - try different page rule syntax */
          @page {
            size: A4 portrait;
          }

          /* Specific page rule for landscape components */
          .month-trailing-component {
            page: month-trailing;
          }

          @media print {
            .month-trailing-component {
              page: month-trailing;
            }

            @page month-trailing {
              size: letter landscape;
              margin-top : 0.5in;
            }
          }

          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #374151;
            background: white;
          }

          .page-break {
            page-break-before: always;
          }

          .no-break {
            page-break-inside: avoid;
          }

          /* MonthTrailing component specific styles */
          .month-trailing-component {
            page: month-trailing;
            page-break-before: always;
          }

          /* Force landscape orientation for month trailing components */
          .month-trailing-component,
          .month-trailing-component * {
            page: month-trailing !important;
          }

          /* Alternative CSS approach for month trailing */
          .max-w-8xl {
            page: month-trailing;
          }

          /* Data attribute targeting */
          [data-component="month-trailing"] {
            page: month-trailing !important;
            page-break-before: always !important;
          }

          /* Multiple targeting approaches */
          .month-trailing-component,
          [data-component="month-trailing"],
          .max-w-8xl {
            page: month-trailing !important;
          }

          /* Fallback approach using CSS transforms if @page doesn't work */
          @media print {
            .month-trailing-component {
              page: month-trailing;
              transform-origin: top left;
              width: 11in;  /* US Letter width */
              height: 8.5in; /* US Letter height in landscape */
            }
          }

          /* Repeating headers for specific components */
          .repeating-header {
            display: table-header-group !important;
            page-break-inside: avoid;
            page-break-after: avoid;
          }

          .month-trailing-header {
            display: table-header-group !important;
            page-break-inside: avoid;
            page-break-after: avoid;
          }

          /* Table headers that should repeat */
          .table-header-group {
            display: table-header-group !important;
          }

          .profit-loss-table thead {
            display: table-header-group !important;
          }

          .repeating-table-header {
            display: table-header-group !important;
            page-break-inside: avoid !important;
            page-break-after: avoid !important;
          }

          /* Ensure all table headers in components with repeating headers repeat */
          .repeating-header ~ * table thead,
          .month-trailing-component table thead,
          .profit-loss-section table thead {
            display: table-header-group !important;
            page-break-inside: avoid !important;
          }

          /* Specific styles for different component types */
          .profit-loss-section .report-header,
          .balance-sheet-section .report-header {
            display: table-header-group !important;
            page-break-inside: avoid !important;
            page-break-after: avoid !important;
          }

          /* Minimal styles - rely on frozen positions */
          .border-b-4.border-blue-900 {
            border-bottom: 4px solid #1e3a8a !important;
            page-break-inside: avoid;
            display: table-header-group;
          }

          .report-header {
            page-break-inside: avoid;
          }

          .component-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            page-break-inside: avoid;
          }

          .metrics-flex{
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
          }

          .metrics-flex > * {
            flex: 1 1 0% !important;
            text-align: center;
          }

          .component-content {
            display: table-row-group;
          }

          .min-h-screen {
            display: table !important;
            width: 100% !important;
            min-height: auto !important;
            page-break-inside: avoid;
          }

          .min-h-screen:not(:first-child) {
            page-break-before: always;
          }

          /* Trust the frozen chart and legend positions */
          div[id*="apex"] {
            page-break-inside: avoid;
          }

          .apexcharts-legend {
            page-break-inside: avoid;
          }

          .bg-gray-200 {
            background-color: #ffffff !important;
          }

          .bg-white {
            background-color: #ffffff !important;
          }

          .overflow-y-auto {
            overflow: visible !important;
          }
        </style>
      </head>
      <body>
        <div class="report-container">
          ${clonedContent.innerHTML}
        </div>
      </body>
      </html>
    `;

    console.log("Html content prepared for PDF generation with frozen charts and legends", htmlContent);

    // Check if any components need disclaimer footer
    const hasDisclaimerComponents = clonedContent.querySelector('[data-needs-disclaimer-footer="true"]') !== null;

    // Generate PDF using the HTML content
    const { generatePDFFromHTML } = await import('../../services/pdf');
    const response = await generatePDFFromHTML(htmlContent, {
      format: 'A4',
      orientation: 'portrait',
      printBackground: true,
      margin: {
        top: '0in',
        right: '0in',
        bottom: '1.2in', // Increased bottom margin to accommodate disclaimer footer
        left: '0in'
      },
      preferCSSPageSize: true,  // Enable CSS @page rules for mixed orientations
      hasDisclaimerFooter: hasDisclaimerComponents // Pass flag to backend
    });

    if (response.success && response.data.pdf) {
      const filename = `Custom_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      const downloadSuccess = downloadPDFFromBase64(response.data.pdf, filename);

      if (downloadSuccess) {
        setSuccessMessage('PDF downloaded successfully!');
        setShowSuccess(true);
      } else {
        throw new Error('Failed to download PDF');
      }
    } else {
      throw new Error(response.message || 'Failed to generate PDF');
    }

  } catch (error) {
    console.error('PDF Generation Error:', error);
    setSuccessMessage('Failed to generate PDF. Please try again.');
    setShowSuccess(true);
  } finally {
    setIsGeneratingPDF(false);
  }
};

  // Initialize data on component mount
  useEffect(() => {
    initializeSettings();
    fetchReportData();
    fetchContentSettings(); // NEW: Fetch content settings
  }, [companyId]); // Add companyId as dependency

  const handleBackToDashboard = () => {
    navigate(`/company/${companyId}`, {
      state: { activeTab: 'reports' }
    });
  };

  const handleSettingChange = (section, property, value) => {
    // Apply font size constraints
    if (property === 'fontSize') {
      const constraints = fontSizeConstraints[section];
      if (constraints) {
        value = Math.max(constraints.min, Math.min(constraints.max, value));
      }
    }

    setTemplateSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [property]: value
      }
    }));
  };

  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  // Save settings to API
  const saveSettingsToAPI = async () => {
    try {
      setIsSavingSettings(true);

      const payload = {
        settings: templateSettings
      };

      const response = await axiosInstance.put('/template-settings', payload);

      if (response.data && response.data.success) {
        // Update localStorage with the saved settings
        localStorage.setItem('templateSettings', JSON.stringify(templateSettings));

        // Update initial settings to current settings
        setInitialSettings(templateSettings);

        setSuccessMessage('Settings saved successfully and applied globally!');
        setShowSuccess(true);

        console.log('Settings saved successfully:', response.data);
      } else {
        throw new Error('Failed to save template settings');
      }
    } catch (error) {
      console.error('Error saving template settings:', error);
      setSuccessMessage('Failed to save settings. Please try again.');
      setShowSuccess(true);
    } finally {
      setIsSavingSettings(false);
    }
  };

  // Button handlers
  const handleSave = () => {
    setShowSaveConfirmModal(true);
  };

  const handleConfirmSave = async () => {
    setShowSaveConfirmModal(false);
    await saveSettingsToAPI();
  };

  const handleCancelSave = () => {
    setShowSaveConfirmModal(false);
  };

  const handleResetToDefault = () => {
    setShowResetConfirmModal(true);
  };

  const handleConfirmReset = () => {
    try {
      // Reset to initial settings loaded from API/localStorage
      if (initialSettings) {
        setTemplateSettings(initialSettings);
        setSelectedTemplate('Deepsight');

        // Update localStorage with initial settings
        localStorage.setItem('templateSettings', JSON.stringify(initialSettings));

        setSuccessMessage('Settings reset to initial values successfully!');
        setShowSuccess(true);
      }
    } catch (error) {
      console.error('Error resetting settings:', error);
      setSuccessMessage('Error resetting settings. Please try again.');
      setShowSuccess(true);
    }
    setShowResetConfirmModal(false);
  };

  const handleCancelReset = () => {
    setShowResetConfirmModal(false);
  };


  const handleResync = () => {
    fetchReportData();
    fetchContentSettings(); // Also resync content settings
    setSuccessMessage('Data resynced successfully!');
    setShowSuccess(true);
  };



  // Convert font type to CSS font-weight
  const getFontWeight = (fontType) => {
    const weights = {
      'Regular': '400',
      'Bold': '700'
    };
    return weights[fontType] || '400';
  };

  const getHeaderStyle = () => ({
    fontFamily: templateSettings?.header?.fontStyle || 'Helvetica',
    fontWeight: getFontWeight(templateSettings?.header?.fontType || 'Bold'),
    fontSize: `${templateSettings?.header?.fontSize || 44}px`,
    color: templateSettings?.header?.color || '#1e7c8c',
    borderRadius: '8px 8px 0 0',
    margin: '0',
  });

  const getHeadingStyle = () => ({
    fontFamily: templateSettings?.heading?.fontStyle || 'Helvetica',
    fontWeight: getFontWeight(templateSettings?.heading?.fontType || 'Bold'),
    fontSize: `${templateSettings?.heading?.fontSize || 36}px`,
    color: templateSettings?.heading?.color || '#1e7c8c',
  });

  const getContentStyle = () => ({
    fontFamily: templateSettings?.content?.fontStyle || 'Helvetica',
    fontWeight: getFontWeight(templateSettings?.content?.fontType || 'Regular'),
    fontSize: `${templateSettings?.content?.fontSize || 15}px`,
    color: templateSettings?.content?.color || '#333333',
    lineHeight: '1.6',
    margin: '0'
  });

  const getSubHeadingStyle = () => ({
    fontFamily: templateSettings?.subHeading?.fontStyle || 'Helvetica',
    fontWeight: getFontWeight(templateSettings?.subHeading?.fontType || 'Bold'),
    fontSize: `${templateSettings?.subHeading?.fontSize || 22}px`,
    color: templateSettings?.subHeading?.color || '#1e7c8c',
    padding: '0'
  });

  // Show loading state while fetching initial settings or content settings
  if (isLoadingSettings || isLoadingContentSettings || !templateSettings) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <CircularProgress size={40} />
          <div className="mt-4 text-lg text-gray-600">Loading settings...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Snackbar for success/error messages */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={4000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSuccess}
          severity={dataError || settingsError || contentSettingsError ? "error" : "success"}
          variant="filled"
          sx={{
            backgroundColor: dataError || settingsError || contentSettingsError ? '#d32f2f' : '#1976d2',
            '& .MuiAlert-icon': {
              color: 'white',
            },
          }}
        >
          {successMessage}
        </Alert>
      </Snackbar>

      {/* Save Confirmation Modal */}
      <Dialog
        open={showSaveConfirmModal}
        onClose={handleCancelSave}
        aria-labelledby="save-dialog-title"
        aria-describedby="save-dialog-description"
      >
        <DialogTitle id="save-dialog-title">
          Confirm Save Settings
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="save-dialog-description">
            Are you sure you want to save these template settings? These settings will be applied globally and will affect all future reports.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelSave} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSave}
            color="primary"
            variant="contained"
            disabled={isSavingSettings}
          >
            {isSavingSettings ? (
              <>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Saving...
              </>
            ) : (
              'Save Settings'
            )}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reset Confirmation Modal */}
      <Dialog
        open={showResetConfirmModal}
        onClose={handleCancelReset}
        aria-labelledby="reset-dialog-title"
        aria-describedby="reset-dialog-description"
      >
        <DialogTitle id="reset-dialog-title">
          Confirm Reset Settings
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="reset-dialog-description">
            Are you sure you want to reset the template settings? This will restore the settings to their initial values when you first loaded this page. Any unsaved changes will be lost.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelReset} color="primary">
            Cancel
          </Button>
          <Button onClick={handleConfirmReset} color="primary" variant="contained">
            Reset Settings
          </Button>
        </DialogActions>
      </Dialog>

      {/* Fixed Sidebar - Full Height */}
      <div className="w-96 flex-shrink-0 h-screen flex flex-col fixed left-0 top-0 z-10 bg-gray-50">
        <div className="px-4 py-[14px] border-b bg-gray-50 flex items-center shadow">
          <Tooltip title="Back to Dashboard" placement="bottom">
            <IconButton
              onClick={handleBackToDashboard}
              sx={{
                color: 'rgb(75, 85, 99)',
                padding: '6px',
                marginRight: '12px',
                '&:hover': {
                  backgroundColor: 'rgba(75, 85, 99, 0.1)',
                },
                '&:focus': {
                  outline: 'none',
                },
                transition: 'all 0.2s',
              }}
            >
              <ArrowBack fontSize="medium" />
            </IconButton>
          </Tooltip>
          <h2 className="text-lg font-semibold text-gray-900">Customize Template</h2>
        </div>

        <div className="p-4 space-y-6 overflow-y-auto flex-1 shadow">
          {/* Header Section */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Header</h3>

            <div className="mb-3">
              <label className="block text-xs text-gray-600 mb-1">Font Style</label>
              <select
                value={templateSettings.header.fontStyle}
                onChange={(e) => handleSettingChange('header', 'fontStyle', e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {fontStyles.map(font => (
                  <option key={font} value={font}>{font}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-3 gap-2 mb-3">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Font Type</label>
                <select
                  value={templateSettings.header.fontType}
                  onChange={(e) => handleSettingChange('header', 'fontType', e.target.value)}
                  className="w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">
                  Font Size ({fontSizeConstraints.header.min}-{fontSizeConstraints.header.max})
                </label>
                <input
                  type="number"
                  min={fontSizeConstraints.header.min}
                  max={fontSizeConstraints.header.max}
                  value={templateSettings.header.fontSize}
                  onChange={(e) => handleSettingChange('header', 'fontSize', parseInt(e.target.value))}
                  className="w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">Color</label>
                <input
                  type="color"
                  value={templateSettings.header.color}
                  onChange={(e) => handleSettingChange('header', 'color', e.target.value)}
                  className="w-full border border-gray-300  h-9 rounded cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Heading Section */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Heading</h3>

            <div className="mb-3">
              <label className="block text-xs text-gray-600 mb-1">Font Style</label>
              <select
                value={templateSettings.heading.fontStyle}
                onChange={(e) => handleSettingChange('heading', 'fontStyle', e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {fontStyles.map(font => (
                  <option key={font} value={font}>{font}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-3 gap-2 mb-3">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Font Type</label>
                <select
                  value={templateSettings.heading.fontType}
                  onChange={(e) => handleSettingChange('heading', 'fontType', e.target.value)}
                  className="w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">
                  Font Size ({fontSizeConstraints.heading.min}-{fontSizeConstraints.heading.max})
                </label>
                <input
                  type="number"
                  min={fontSizeConstraints.heading.min}
                  max={fontSizeConstraints.heading.max}
                  value={templateSettings.heading.fontSize}
                  onChange={(e) => handleSettingChange('heading', 'fontSize', parseInt(e.target.value))}
                  className="w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">Color</label>
                <input
                  type="color"
                  value={templateSettings.heading.color}
                  onChange={(e) => handleSettingChange('heading', 'color', e.target.value)}
                  className="w-full border border-gray-300 h-9 rounded cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Sub-Heading Section */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Sub-Heading</h3>

            <div className="mb-3">
              <label className="block text-xs text-gray-600 mb-1">Font Style</label>
              <select
                value={templateSettings.subHeading.fontStyle}
                onChange={(e) => handleSettingChange('subHeading', 'fontStyle', e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {fontStyles.map(font => (
                  <option key={font} value={font}>{font}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-3 gap-2 mb-3">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Font Type</label>
                <select
                  value={templateSettings.subHeading.fontType}
                  onChange={(e) => handleSettingChange('subHeading', 'fontType', e.target.value)}
                  className="w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">
                  Font Size ({fontSizeConstraints.subHeading.min}-{fontSizeConstraints.subHeading.max})
                </label>
                <input
                  type="number"
                  min={fontSizeConstraints.subHeading.min}
                  max={fontSizeConstraints.subHeading.max}
                  value={templateSettings.subHeading.fontSize}
                  onChange={(e) => handleSettingChange('subHeading', 'fontSize', parseInt(e.target.value))}
                  className="w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">Color</label>
                <input
                  type="color"
                  value={templateSettings.subHeading.color}
                  onChange={(e) => handleSettingChange('subHeading', 'color', e.target.value)}
                  className="w-full border border-gray-300 h-9 rounded cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Content</h3>

            <div className="mb-3">
              <label className="block text-xs text-gray-600 mb-1">Font Style</label>
              <select
                value={templateSettings.content.fontStyle}
                onChange={(e) => handleSettingChange('content', 'fontStyle', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {fontStyles.map(font => (
                  <option key={font} value={font}>{font}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-3 gap-2 mb-3">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Font Type</label>
                <select
                  value={templateSettings.content.fontType}
                  onChange={(e) => handleSettingChange('content', 'fontType', e.target.value)}
                  className="w-full px-2 py-2 border border-gray-300  rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">
                  Font Size ({fontSizeConstraints.content.min}-{fontSizeConstraints.content.max})
                </label>
                <input
                  type="number"
                  min={fontSizeConstraints.content.min}
                  max={fontSizeConstraints.content.max}
                  value={templateSettings.content.fontSize}
                  onChange={(e) => handleSettingChange('content', 'fontSize', parseInt(e.target.value))}
                  className="w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-600 mb-1">Color</label>
                <input
                  type="color"
                  value={templateSettings.content.color}
                  onChange={(e) => handleSettingChange('content', 'color', e.target.value)}
                  className="w-full border border-gray-300 h-9 rounded cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="pt-7 border-t border-gray-300">
            <div className="flex flex-row gap-4">
              <button
                onClick={handleSave}
                disabled={isSavingSettings}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-neutral-800"
              >
                {isSavingSettings ? (
                  <>
                    <CircularProgress size={14} sx={{ mr: 1 }} />
                    SAVING...
                  </>
                ) : (
                  'SAVE'
                )}
              </button>

              <button
                onClick={handleResetToDefault}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-neutral-800"
              >
                RESET
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area - Header and Content */}
      <div className="flex flex-col flex-1 h-screen" style={{ marginLeft: '384px' }}>
        {/* Fixed Header */}
        <div className="px-6 py-3 flex items-center bg-gray-50 justify-end fixed top-0 z-5 shadow"
          style={{ left: '384px', right: '0' }}>

          <div className="flex items-center justify-end space-x-3">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title={isLoadingData ? "Loading data..." : "Resync data"} placement="top">
                <span>
                  <IconButton
                    onClick={handleResync}
                    disabled={isLoadingData}
                    sx={{
                      color: isLoadingData ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',
                      '&:hover': {
                        backgroundColor: 'rgba(156, 163, 175, 0.1)',
                      },
                      '&:focus': {
                        outline: 'none',
                      },
                      '&:disabled': {
                        color: 'rgba(156, 163, 175, 0.6)',
                        cursor: 'not-allowed',
                      },
                      transition: 'all 0.2s',
                      padding: '8px',
                    }}
                  >
                    {isLoadingData ? (
                      <CircularProgress size={16} color="inherit" />
                    ) : (
                      <SyncIcon fontSize="medium" />
                    )}
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title={isGeneratingPDF ? "Generating PDF..." : "Download PDF"} placement="top">
                <span>
                  <IconButton
                    onClick={handleDownloadPDF}
                    disabled={isGeneratingPDF || isLoadingData}
                    sx={{
                      color: (isGeneratingPDF || isLoadingData) ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',
                      '&:hover': {
                        backgroundColor: 'rgba(156, 163, 175, 0.1)',
                      },
                      '&:focus': {
                        outline: 'none',
                      },
                      '&:disabled': {
                        color: 'rgba(156, 163, 175, 0.6)',
                        cursor: 'not-allowed',
                      },
                      transition: 'all 0.2s',
                      padding: '8px',
                    }}
                  >
                    {isGeneratingPDF ? (
                      <CircularProgress size={16} color="inherit" />
                    ) : (
                      <DownloadIcon fontSize="medium" />
                    )}
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </div>
        </div>

        {/* Scrollable Content Panel */}
        <div
          className="flex-1 overflow-y-auto bg-gray-200"
          style={{ marginTop: '60px', width: '100%' }}
        >
          {isLoadingData ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <CircularProgress size={40} />
                <div className="mt-4 text-lg text-gray-600">Loading report data...</div>
              </div>
            </div>
          ) : dataError ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-lg text-red-600 mb-4">Failed to load report data</div>
                <button
                  onClick={fetchReportData}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : (
            <>

              <DeepSightCoverPage
              reportData={reportData}
              />

              <TableOfContents
                headingTextStyle={getHeadingStyle()}
                contentTextStyle={getContentStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                reportData={reportData}
              />
              <ReportSummary
                headerTextStyle={getHeaderStyle()}
                headingTextStyle={getHeadingStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                reportData={reportData}
              />
              <FiscalYearDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                fiscalData={reportData}
                contentSettings={contentSettings}
              />
              <ExpenseSummaryDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                reportData={reportData}
                contentSettings={contentSettings}
              />
              <OperationalEfficiencyDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                operationalData={reportData}
              />
              <LiquiditySummaryDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                liquidityData={reportData}
              />
              <ProfitLoss13MonthDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                reportData={reportData}
              />
              <ProfitLossMonthlyDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                reportData={reportData}

              />
              <ProfitLossYTDDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                reportData={reportData}
              />
              <BalanceSheetDashboard
                headerTextStyle={getHeaderStyle()}
                subHeadingTextStyle={getSubHeadingStyle()}
                contentTextStyle={getContentStyle()}
                reportData={reportData}

              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomizeTemplateWithPreview;