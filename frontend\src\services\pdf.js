import axiosInstance from "./axiosInstance";

export const generatePDFFromHTML = async (htmlContent, options = {}) => {
  try {
    const response = await axiosInstance.post("/pdf/generate-from-html", {
      htmlContent,
      options
    });

    return response.data;
  } catch (error) {
    console.error('PDF HTML Generation Error:', error);
    throw error;
  }
};

export const downloadPDFFromBase64 = (base64PDF, filename = 'report.pdf') => {
  try {
    // Convert base64 to blob
    const byteCharacters = atob(base64PDF);
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'application/pdf' });

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;

    // Trigger download
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return true;
  } catch (error) {
    console.error('PDF Download Error:', error);
    return false;
  }
};