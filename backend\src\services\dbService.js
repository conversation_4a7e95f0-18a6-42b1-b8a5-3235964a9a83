import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
import { flattenFinancialData } from '../utils/parseData.js';

// Financial report queries
// export const reportQueries = {
//   ytdTotalIncome: (realmId) => `
//     SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_Income"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" = 'Income'
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   ytdCOGS: (realmId) => `
//     SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_COGS"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" = 'Cost of Goods Sold'
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   ytdTotalExpense: (realmId) => `
//     SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_Expense"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" = 'Expense'
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   ytdNetProfit: (realmId) => `
//     SELECT
//       COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) -
//       COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) -
//       COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" ELSE 0 END), 0) AS "YTD_Net_Profit"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" IN ('Income', 'Cost of Goods Sold', 'Expense')
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   netIncomeMonthly: (realmId) => `
//     SELECT
//       pl."year",
//       pl."month",
//       COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) AS "Income",
//       COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) AS "COGS",
//       COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" ELSE 0 END), 0) AS "Expense"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     WHERE pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 1) OR
//       (pl."year" = 2025 AND pl."month" <= 1)
//     )
//     GROUP BY pl."year", pl."month"
//     ORDER BY pl."year", pl."month";
//   `,

//   grossProfitMargin: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     cogs AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_cogs
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Cost of Goods Sold'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(i.total_income, 0) AS total_income,
//       COALESCE(c.total_cogs, 0) AS total_cogs,
//       COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) AS gross_profit,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE ((COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0)) / COALESCE(i.total_income, 1)) * 100
//         END, 2
//       ) AS gross_profit_margin_percentage
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN cogs c ON m.year = c.year AND m.month = c.month
//     ORDER BY m.year, m.month;
//   `,

//   netProfitMargin: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//       ORDER BY "year", "month"
//     ),
//     cogs AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_cogs
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Cost of Goods Sold'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//       ORDER BY "year", "month"
//     ),
//     expenses AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_expenses
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Expense'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//       ORDER BY "year", "month"
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(i.total_income, 0) AS total_income,
//       COALESCE(c.total_cogs, 0) AS total_cogs,
//       COALESCE(e.total_expenses, 0) AS total_expenses,
//       COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0) AS net_profit,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE ((COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0)) / COALESCE(i.total_income, 1)) * 100
//         END, 2
//       ) AS net_profit_margin_percentage
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN cogs c ON m.year = c.year AND m.month = c.month
//     LEFT JOIN expenses e ON m.year = e.year AND m.month = e.month
//     ORDER BY m.year, m.month;
//   `,

//   expensesTopAccountsMonthly : (realmId) => `
//     WITH expense_data AS (
//   SELECT
//     a."name" AS account_name,
//     pr."year",
//     pr."month",
//     SUM(pr."amount") AS monthly_expense
//   FROM
//     "ProfitLossReport" pr
//   JOIN
//     "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
//   WHERE
//     pr."realmId" = '${realmId}'
//     AND a."type" = 'Expense'
//     AND (
//       (pr."year" = 2024 AND pr."month" >= 4) OR
//       (pr."year" = 2025 AND pr."month" <= 3)
//     )
//   GROUP BY
//     a."name", pr."year", pr."month"
// ),
// total_by_account AS (
//   SELECT
//     account_name,
//     SUM(monthly_expense) AS total_expense
//   FROM expense_data
//   GROUP BY account_name
// ),
// top_accounts AS (
//   SELECT account_name
//   FROM total_by_account
//   ORDER BY total_expense DESC
//   LIMIT 10
// ),
// classified_expenses AS (
//   SELECT
//     CASE
//       WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
//       THEN ed.account_name
//       ELSE 'Other'
//     END AS account_name,
//     ed.year,
//     ed.month,
//     SUM(ed.monthly_expense) AS monthly_expense
//   FROM expense_data ed
//   GROUP BY
//     CASE
//       WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
//       THEN ed.account_name
//       ELSE 'Other'
//     END,
//     ed.year, ed.month
// ),
// pivoted AS (
//   SELECT
//     account_name,
//     SUM(CASE WHEN year = 2024 AND month = 4 THEN monthly_expense ELSE 0 END) AS apr_24,
//     SUM(CASE WHEN year = 2024 AND month = 5 THEN monthly_expense ELSE 0 END) AS may_24,
//     SUM(CASE WHEN year = 2024 AND month = 6 THEN monthly_expense ELSE 0 END) AS jun_24,
//     SUM(CASE WHEN year = 2024 AND month = 7 THEN monthly_expense ELSE 0 END) AS jul_24,
//     SUM(CASE WHEN year = 2024 AND month = 8 THEN monthly_expense ELSE 0 END) AS aug_24,
//     SUM(CASE WHEN year = 2024 AND month = 9 THEN monthly_expense ELSE 0 END) AS sep_24,
//     SUM(CASE WHEN year = 2024 AND month = 10 THEN monthly_expense ELSE 0 END) AS oct_24,
//     SUM(CASE WHEN year = 2024 AND month = 11 THEN monthly_expense ELSE 0 END) AS nov_24,
//     SUM(CASE WHEN year = 2024 AND month = 12 THEN monthly_expense ELSE 0 END) AS dec_24,
//     SUM(CASE WHEN year = 2025 AND month = 1 THEN monthly_expense ELSE 0 END) AS jan_25,
//     SUM(CASE WHEN year = 2025 AND month = 2 THEN monthly_expense ELSE 0 END) AS feb_25,
//     SUM(CASE WHEN year = 2025 AND month = 3 THEN monthly_expense ELSE 0 END) AS mar_25,
//     SUM(monthly_expense) AS total_expense
//   FROM classified_expenses
//   GROUP BY account_name
// )
// SELECT *
// FROM pivoted
// ORDER BY total_expense DESC;

//   `,

//   expensesTopAccounts: (realmId) => `
//     WITH expense_data AS (
//       SELECT
//         a."name" AS account_name,
//         SUM(pr."amount") AS total_expense
//       FROM
//         "ProfitLossReport" pr
//       JOIN
//         "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
//       WHERE
//         pr."realmId" = '${realmId}'
//         AND a."type" = 'Expense'
//         AND (
//           (pr."year" = 2024 AND pr."month" >= 4) OR
//           (pr."year" = 2025 AND pr."month" <= 3)
//         )
//       GROUP BY
//         a."name"
//     ),
//     top_accounts AS (
//       SELECT *
//       FROM expense_data
//       ORDER BY total_expense DESC
//       LIMIT 10
//     ),
//     other_total AS (
//       SELECT
//         'Other' AS account_name,
//         SUM(ed.total_expense) AS total_expense
//       FROM expense_data ed
//       WHERE ed.account_name NOT IN (SELECT account_name FROM top_accounts)
//     ),
//     combined AS (
//       SELECT * FROM top_accounts
//       UNION ALL
//       SELECT * FROM other_total
//     ),
//     total_sum AS (
//       SELECT SUM(total_expense) AS grand_total FROM combined
//     )
//     SELECT
//       c.account_name,
//       c.total_expense,
//       ROUND((c.total_expense / t.grand_total) * 100, 2) AS percentage_of_total
//     FROM
//       combined c,
//       total_sum t
//     ORDER BY
//       c.total_expense DESC;
//   `,

//   daysSalesAROutstanding: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT "year", "month", SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     ar AS (
//       SELECT "year", "month", SUM("total") AS accounts_receivable
//       FROM "AccountReceivableAgingSummaryReport"
//       WHERE "realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     days_in_month AS (
//       SELECT year, month,
//              EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(year || '-' || month || '-01', 'YYYY-MM-DD'))
//              + INTERVAL '1 month - 1 day'))::int AS days
//       FROM months
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(ar.accounts_receivable, 0) AS accounts_receivable,
//       COALESCE(i.total_income, 0) AS total_income,
//       d.days AS days_in_month,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE COALESCE(ar.accounts_receivable, 0) / (i.total_income / d.days)
//         END, 2
//       ) AS days_sales_outstanding
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN ar ON m.year = ar.year AND m.month = ar.month
//     LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
//     ORDER BY m.year, m.month;
//   `,

//   daysSalesAPOutstanding: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT "year", "month", SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     ap AS (
//       SELECT "year", "month", SUM("total") AS accounts_payable
//       FROM "AccountPayableAgingSummaryReport"
//       WHERE "realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     days_in_month AS (
//       SELECT year, month,
//              EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(year || '-' || month || '-01', 'YYYY-MM-DD'))
//              + INTERVAL '1 month - 1 day'))::int AS days
//       FROM months
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(ap.accounts_payable, 0) AS accounts_payable,
//       COALESCE(i.total_income, 0) AS total_income,
//       d.days AS days_in_month,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE COALESCE(ap.accounts_payable, 0) / (i.total_income / d.days)
//         END, 2
//       ) AS days_payables_outstanding
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN ap ON m.year = ap.year AND m.month = ap.month
//     LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
//     ORDER BY m.year, m.month;
//   `,

//   daysInventoryOutstanding: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3) AS month
//     ),
//     inventory AS (
//       SELECT
//         b."year",
//         b."month",
//         MAX(b."statementAmount") AS inventory_balance
//       FROM "BalanceSheetReport" b
//       JOIN "Account" a ON a."accountId" = b."accountId"
//       WHERE b."realmId" = '${realmId}'
//         AND a."accountClassification" = 'Asset'
//         AND (
//           a."name" ILIKE '%inventory%' OR
//           a."accountSubTypeName" ILIKE '%inventory%'
//         )
//       GROUP BY b."year", b."month"
//     ),
//     cogs AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_cogs
//       FROM (
//         SELECT DISTINCT ON (p."year", p."month", p."accountId")
//           p."year",
//           p."month",
//           p."accountId",
//           p."amount"
//         FROM "ProfitLossReport" p
//         JOIN "Account" a ON a."accountId" = p."accountId"
//         WHERE p."realmId" = '${realmId}'
//           AND a."type" = 'Cost of Goods Sold'
//       ) deduped
//       GROUP BY "year", "month"
//     ),
//     days_in_month AS (
//       SELECT
//         m.year,
//         m.month,
//         EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(m.year, m.month, 1))
//                           + INTERVAL '1 month - 1 day'))::int AS days
//       FROM months m
//     ),
//     avg_inventory AS (
//       SELECT
//         curr.year,
//         curr.month,
//         (COALESCE(prev.inventory_balance, curr.inventory_balance) + curr.inventory_balance) / 2.0 AS average_inventory
//       FROM inventory curr
//       LEFT JOIN inventory prev
//         ON (prev.year = curr.year AND prev.month = curr.month - 1)
//         OR (prev.year = curr.year - 1 AND prev.month = 12 AND curr.month = 1)
//     )
//     SELECT
//       m.year,
//       m.month,
//       ai.average_inventory,
//       c.total_cogs,
//       d.days,
//       ROUND(
//         CASE
//           WHEN c.total_cogs IS NULL OR c.total_cogs = 0 THEN 0
//           ELSE ai.average_inventory / (c.total_cogs / d.days)
//         END, 2
//       ) AS days_inventory_outstanding
//     FROM months m
//     LEFT JOIN avg_inventory ai ON ai.year = m.year AND ai.month = m.month
//     LEFT JOIN cogs c ON c.year = m.year AND c.month = m.month
//     LEFT JOIN days_in_month d ON d.year = m.year AND d.month = m.month
//     ORDER BY year, month;
//   `,

//   netChangeInCash: (realmId) => `
//     WITH month_series AS (
//       SELECT EXTRACT(YEAR FROM d)::INT AS "year",
//              EXTRACT(MONTH FROM d)::INT AS "month"
//       FROM generate_series(
//         DATE '2024-04-01',
//         DATE '2025-04-01',
//         INTERVAL '1 month'
//       ) d
//     ),
//     monthly_cash AS (
//       SELECT
//         b."year",
//         b."month",
//         SUM(b."statementAmount") AS closing_balance
//       FROM "BalanceSheetReport" b
//       JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
//       WHERE a."type" IN ('Bank', 'Cash')
//         AND b."realmId" = '${realmId}'
//       GROUP BY b."year", b."month"
//     ),
//     cash_change AS (
//       SELECT
//         m."year",
//         m."month",
//         mc.closing_balance,
//         LAG(mc.closing_balance) OVER (ORDER BY m."year", m."month") AS opening_balance
//       FROM month_series m
//       LEFT JOIN monthly_cash mc ON m."year" = mc."year" AND m."month" = mc."month"
//     )
//     SELECT
//       "year",
//       "month",
//       CASE
//         WHEN closing_balance IS NULL OR opening_balance IS NULL THEN '0.00'
//         ELSE (closing_balance - opening_balance)::TEXT
//       END AS net_change_in_cash
//     FROM cash_change
//     ORDER BY "year", "month";
//   `,

//   quickRatio: (realmId) => `
//     WITH month_series AS (
//   SELECT
//     EXTRACT(YEAR FROM d)::INT AS "year",
//     EXTRACT(MONTH FROM d)::INT AS "month"
//   FROM generate_series(
//     DATE '2024-04-01',
//     DATE '2025-04-01',
//     INTERVAL '1 month'
//   ) d
// ),
// balances AS (
//   SELECT
//     b."year",
//     b."month",
//     b."statementAmount" AS amount,
//     a."type",
//     a."accountSubTypeName"
//   FROM "BalanceSheetReport" b
//   JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
//   WHERE b."realmId" = '${realmId}'
// ),
// monthly_aggregates AS (
//   SELECT
//     "year",
//     "month",
//     SUM(CASE WHEN "type" = 'Bank' THEN amount ELSE 0 END) AS cash,
//     SUM(CASE WHEN "type" = 'Accounts Receivable' THEN amount ELSE 0 END) AS accounts_receivable,
//     SUM(CASE WHEN "accountSubTypeName" IN ('CreditCard', 'AccountsPayable', 'OtherCurrentLiabilities') THEN amount ELSE 0 END) AS current_liabilities
//   FROM balances
//   GROUP BY "year", "month"
// ),
// full_view AS (
//   SELECT
//     m."year",
//     m."month",
//     COALESCE(ma.cash, 0) AS cash,
//     COALESCE(ma.accounts_receivable, 0) AS accounts_receivable,
//     COALESCE(ma.current_liabilities, 0) AS current_liabilities
//   FROM month_series m
//   LEFT JOIN monthly_aggregates ma ON m."year" = ma."year" AND m."month" = ma."month"
// )
// SELECT
//   "year",
//   "month",
//   cash,
//   accounts_receivable,
//   current_liabilities,
//   CASE
//     WHEN current_liabilities = 0 THEN '0.00'
//     ELSE ROUND((cash + accounts_receivable) / current_liabilities, 2)::TEXT
//   END AS quick_ratio
// FROM full_view
// ORDER BY "year", "month";

//   `,

//   roeRoa : (realmId) => `
//   WITH pnl AS (
//   SELECT
//     pl."year",
//     pl."month",
//     COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) AS income,
//     COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) AS cogs,
//     COALESCE(SUM(CASE WHEN a."type" IN ('Expense', 'Other Expense') THEN pl."amount" ELSE 0 END), 0) AS expense,
//     COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) -
//     COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) -
//     COALESCE(SUM(CASE WHEN a."type" IN ('Expense', 'Other Expense') THEN pl."amount" ELSE 0 END), 0) AS net_profit
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
//   WHERE pl."realmId" = '${realmId}'
//     AND (
//       ("year" = 2024 AND "month" >= 4) OR
//       ("year" = 2025 AND "month" <= 3)
//     )
//   GROUP BY pl."year", pl."month"
// ),
// bs AS (
//   SELECT
//     bs."year",
//     bs."month",
//     COALESCE(SUM(CASE WHEN a."type" = 'Asset' THEN bs."statementAmount" ELSE 0 END), 0) AS total_assets,
//     COALESCE(SUM(CASE WHEN a."type" = 'Equity' THEN bs."statementAmount" ELSE 0 END), 0) AS total_equity
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId"
//   WHERE bs."realmId" = '${realmId}'
//     AND (
//       (bs."year" = 2024 AND bs."month" >= 4) OR
//       (bs."year" = 2025 AND bs."month" <= 3)
//     )
//   GROUP BY bs."year", bs."month"
// )
// SELECT
//   pnl."year",
//   pnl."month",
//   pnl.income,
//   pnl.cogs,
//   pnl.expense,
//   pnl.net_profit,
//   bs.total_assets,
//   bs.total_equity,
//   ROUND(COALESCE(CASE WHEN bs.total_assets <> 0 THEN pnl.net_profit / bs.total_assets ELSE 0 END, 0), 2) AS roa,
//   ROUND(COALESCE(CASE WHEN bs.total_equity <> 0 THEN pnl.net_profit / bs.total_equity ELSE 0 END, 0), 2) AS roe
// FROM pnl
// LEFT JOIN bs ON pnl."year" = bs."year" AND pnl."month" = bs."month"
// ORDER BY pnl."year", pnl."month";
//   `,

//   cashConversionCycle : (realmId) => `
//   WITH ar AS (
//   SELECT "year", "month", COALESCE(SUM("total"), 0) AS ar_balance
//   FROM "AccountReceivableAgingSummaryReport"
//   WHERE "realmId" = '${realmId}'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY "year", "month"
// ),
// ap AS (
//   SELECT "year", "month", COALESCE(SUM("total"), 0) AS ap_balance
//   FROM "AccountPayableAgingSummaryReport"
//   WHERE "realmId" = '${realmId}'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY "year", "month"
// ),
// inventory AS (
//   SELECT bs."year", bs."month", COALESCE(SUM(bs."statementAmount"), 0) AS inventory_balance
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId" AND bs."userId" = a."userId"
//   WHERE bs."realmId" = '${realmId}'
//     AND a."type" = 'Asset'
//     AND a."name" ILIKE '%inventory%'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY bs."year", bs."month"
// ),
// revenue AS (
//   SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS income
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Income'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY pl."year", pl."month"
// ),
// cogs AS (
//   SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS cogs
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Cost of Goods Sold'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY pl."year", pl."month"
// )
// SELECT
//   COALESCE(re."year", co."year", ar."year", ap."year", inv."year") AS "year",
//   COALESCE(re."month", co."month", ar."month", ap."month", inv."month") AS "month",
//   COALESCE(re.income, 0) AS income,
//   COALESCE(ar.ar_balance, 0) AS ar_balance,
//   COALESCE(co.cogs, 0) AS cogs,
//   COALESCE(inv.inventory_balance, 0) AS inventory_balance,
//   COALESCE(ap.ap_balance, 0) AS ap_balance,
//   ROUND(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (COALESCE(ar.ar_balance, 0) / re.income) * 30 ELSE 0 END, 2) AS dso,
//   ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(inv.inventory_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dio,
//   ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(ap.ap_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dpo,
//   ROUND(
//     COALESCE(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (ar.ar_balance / re.income) * 30 ELSE 0 END, 0) +
//     COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (inv.inventory_balance / co.cogs) * 30 ELSE 0 END, 0) -
//     COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (ap.ap_balance / co.cogs) * 30 ELSE 0 END, 0),
//     2
//   ) AS ccc
// FROM revenue re
// FULL OUTER JOIN cogs co ON re."year" = co."year" AND re."month" = co."month"
// FULL OUTER JOIN ar ON re."year" = ar."year" AND re."month" = ar."month"
// FULL OUTER JOIN ap ON re."year" = ap."year" AND re."month" = ap."month"
// FULL OUTER JOIN inventory inv ON re."year" = inv."year" AND re."month" = inv."month"
// ORDER BY "year", "month";
//   `,

//   fixedAssetTurnover: (realmId) => `
//   WITH revenue AS (
//   SELECT
//     pl."year",
//     pl."month",
//     COALESCE(SUM(pl."amount"), 0) AS total_revenue
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON
//     pl."accountId" = a."accountId" AND
//     pl."realmId" = a."realmId" AND
//     pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Income'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4) OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     )
//   GROUP BY pl."year", pl."month"
// ),
// fixed_assets AS (
//   SELECT
//     bs."year",
//     bs."month",
//     COALESCE(SUM(bs."statementAmount"), 0) AS net_fixed_assets
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON
//     bs."accountId" = a."accountId" AND
//     bs."realmId" = a."realmId" AND
//     bs."userId" = a."userId"
//   WHERE bs."realmId" = '${realmId}'
//     AND a."type" = 'Asset'
//     AND a."name" ILIKE '%fixed%'
//     AND (
//       (bs."year" = 2024 AND bs."month" >= 3) OR
//       (bs."year" = 2025 AND bs."month" <= 3)
//     )
//   GROUP BY bs."year", bs."month"
// ),
// combined AS (
//   SELECT
//     r."year",
//     r."month",
//     COALESCE(r.total_revenue, 0) AS total_revenue,
//     COALESCE(fa.net_fixed_assets, 0) AS net_fixed_assets,
//     COALESCE(LAG(fa.net_fixed_assets) OVER (ORDER BY r."year", r."month"), 0) AS prev_fixed_assets
//   FROM revenue r
//   LEFT JOIN fixed_assets fa ON r."year" = fa."year" AND r."month" = fa."month"
// )
// SELECT
//   "year",
//   "month",
//   total_revenue,
//   net_fixed_assets,
//   prev_fixed_assets,
//   ROUND(
//     CASE
//       WHEN (net_fixed_assets + prev_fixed_assets) = 0 THEN 0
//       ELSE total_revenue / ((net_fixed_assets + prev_fixed_assets) / 2)
//     END,
//     2
//   ) AS fat
// FROM combined
// ORDER BY "year", "month";
//   `,

//   monthsCashOnHand : (realmId) => `
//   WITH cash_ar AS (
//   SELECT
//     bs."year",
//     bs."month",
//     COALESCE(SUM(CASE
//       WHEN a."name" ILIKE '%cash%' THEN bs."statementAmount"
//       WHEN a."name" ILIKE '%receivable%' THEN bs."statementAmount"
//       ELSE 0
//     END), 0) AS cash_ar_total
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON
//     bs."accountId" = a."accountId" AND
//     bs."realmId" = a."realmId" AND
//     bs."userId" = a."userId"
//   WHERE bs."realmId" = '${realmId}'
//     AND (
//       (bs."year" = 2024 AND bs."month" >= 4) OR
//       (bs."year" = 2025 AND bs."month" <= 3)
//     )
//   GROUP BY bs."year", bs."month"
// ),
// total_expense AS (
//   SELECT
//     COALESCE(SUM(pl."amount"), 0) AS yearly_expense
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON
//     pl."accountId" = a."accountId" AND
//     pl."realmId" = a."realmId" AND
//     pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Expense'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4) OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     )
// ),
// monthly_avg_expense AS (
//   SELECT ROUND(COALESCE(yearly_expense, 0) / 12.0, 2) AS avg_monthly_expense FROM total_expense
// )
// SELECT
//   ca."year",
//   ca."month",
//   ROUND(COALESCE(ca.cash_ar_total, 0), 2) AS cash_ar_total,
//   ROUND(COALESCE(mae.avg_monthly_expense, 0), 2) AS avg_monthly_expense,
//   ROUND(
//     CASE
//       WHEN COALESCE(mae.avg_monthly_expense, 0) = 0 THEN 0
//       ELSE COALESCE(ca.cash_ar_total, 0) / mae.avg_monthly_expense
//     END,
//     2
//   ) AS months_cash
// FROM cash_ar ca
// CROSS JOIN monthly_avg_expense mae
// ORDER BY ca."year", ca."month";
//   `
// };

export const reportQueries = {
  // YTD Summary Query
  ytdSummary: (realmId, fiscalYearStart, fiscalYearEnd) => `
  SELECT
    COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 0) AS "YTD_Total_Income",
    COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" END), 0) AS "YTD_Total_COGS",
    COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" END), 0) AS "YTD_Total_Expense",
    COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0)
      - COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0)
      - COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" ELSE 0 END), 0)
      AS "YTD_Net_Profit"
  FROM "ProfitLossReport" pl
  JOIN "Account" a
    ON pl."accountId" = a."accountId"
    AND pl."realmId" = a."realmId"
  WHERE pl."realmId" = '${realmId}'
    AND a."type" IN ('Income', 'Cost of Goods Sold', 'Expense')
    AND (
      (pl."year" = ${fiscalYearStart.year} AND pl."month" >= ${fiscalYearStart.month})
      OR
      (pl."year" = ${fiscalYearEnd.year} AND pl."month" <= ${fiscalYearEnd.month})
      OR
      (pl."year" > ${fiscalYearStart.year} AND pl."year" < ${fiscalYearEnd.year})
    );
`,

  // Monthly P&L with Gross Profit Margin
  monthlyProfitLoss: (realmId, fiscalYearStart, fiscalYearEnd) => `
  SELECT
    pl."year",
    pl."month",
    CASE
      WHEN COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 0) = 0 THEN 0
      ELSE ROUND(
        (
          COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 0) -
          COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" END), 0)
        )
        /
        COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 1),
        4
      )
    END AS "Gross_Profit_Margin"
  FROM "ProfitLossReport" pl
  JOIN "Account" a
    ON pl."accountId" = a."accountId"
    AND pl."realmId" = a."realmId"
    WHERE pl."realmId" = '${realmId}'
    AND a."type" IN ('Income', 'Cost of Goods Sold', 'Expense')
    AND (
      (pl."year" = ${fiscalYearStart.year} AND pl."month" >= ${fiscalYearStart.month})
      OR
      (pl."year" = ${fiscalYearEnd.year} AND pl."month" <= ${fiscalYearEnd.month})
      OR
      (pl."year" > ${fiscalYearStart.year} AND pl."year" < ${fiscalYearEnd.year})
    )
  GROUP BY pl."year", pl."month"
  ORDER BY pl."year", pl."month";
`,

  // Monthly Financial Summary with Net Profit Margin
  monthlyFinancialSummary: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH months AS (
    -- Generate all months from fiscal year start to fiscal year end
    SELECT 
      EXTRACT(YEAR FROM date_month)::INTEGER AS year,
      EXTRACT(MONTH FROM date_month)::INTEGER AS month
    FROM (
      SELECT generate_series(
        '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01'::date,
        '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01'::date,
        '1 month'::interval
      ) AS date_month
    ) month_series
  ),
  income AS (
    SELECT
      "year",
      "month",
      SUM("amount") AS total_income
    FROM "ProfitLossReport" pl
    JOIN (
      SELECT DISTINCT "accountId"
      FROM "Account"
      WHERE "type" = 'Income'
        AND "realmId" = '${realmId}'
    ) a ON pl."accountId" = a."accountId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY "year", "month"
    ORDER BY "year", "month"
  ),
  cogs AS (
    SELECT
      "year",
      "month",
      SUM("amount") AS total_cogs
    FROM "ProfitLossReport" pl
    JOIN (
      SELECT DISTINCT "accountId"
      FROM "Account"
      WHERE "type" = 'Cost of Goods Sold'
        AND "realmId" = '${realmId}'
    ) a ON pl."accountId" = a."accountId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY "year", "month"
    ORDER BY "year", "month"
  ),
  expenses AS (
    SELECT
      "year",
      "month",
      SUM("amount") AS total_expenses
    FROM "ProfitLossReport" pl
    JOIN (
      SELECT DISTINCT "accountId"
      FROM "Account"
      WHERE "type" = 'Expense'
        AND "realmId" = '${realmId}'
    ) a ON pl."accountId" = a."accountId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY "year", "month"
    ORDER BY "year", "month"
  )
  SELECT
    m.year,
    m.month,
    COALESCE(i.total_income, 0) AS total_income,
    COALESCE(c.total_cogs, 0) AS total_cogs,
    COALESCE(e.total_expenses, 0) AS total_expenses,
    COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0) AS net_profit,
    ROUND(
      CASE 
        WHEN COALESCE(i.total_income, 0) = 0 THEN 0
        ELSE ((COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0)) / COALESCE(i.total_income, 1)) * 100
      END, 2
    ) AS net_profit_margin_percentage
  FROM months m
  LEFT JOIN income i ON m.year = i.year AND m.month = i.month
  LEFT JOIN cogs c ON m.year = c.year AND m.month = c.month
  LEFT JOIN expenses e ON m.year = e.year AND m.month = e.month
  ORDER BY m.year, m.month;
`,

  // Top Expenses Breakdown
  topExpensesBreakdown: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH expense_data AS (
    SELECT
      a."name" AS account_name,
      SUM(pr."amount") AS total_expense
    FROM "ProfitLossReport" pr
    JOIN "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
    WHERE
      pr."realmId" = '${realmId}'
      AND a."type" = 'Expense'
      AND (
        (pr."year" = ${fiscalYearStart.year} AND pr."month" >= ${fiscalYearStart.month})
        OR
        (pr."year" = ${fiscalYearEnd.year} AND pr."month" <= ${fiscalYearEnd.month})
        OR
        (pr."year" > ${fiscalYearStart.year} AND pr."year" < ${fiscalYearEnd.year})
      )
    GROUP BY a."name"
  ),
  total_sum AS (
    SELECT SUM(total_expense) AS grand_total FROM expense_data
  ),
  top_accounts AS (
    SELECT *
    FROM expense_data
    ORDER BY total_expense DESC
    LIMIT 10
  ),
  other_total AS (
    SELECT
      'Other' AS account_name,
      SUM(ed.total_expense) AS total_expense
    FROM expense_data ed
    WHERE ed.account_name NOT IN (SELECT account_name FROM top_accounts)
  ),
  combined AS (
    SELECT * FROM top_accounts
    UNION ALL
    SELECT * FROM other_total
  )
  SELECT
    c.account_name,
    c.total_expense,
    ROUND((c.total_expense / ts.grand_total) * 100, 2) AS percentage_of_total
  FROM combined c, total_sum ts
  ORDER BY total_expense DESC NULLS LAST;
`,
  topExpensesMonthWise: (
    realmId,
    fiscalYearStart,
    fiscalYearEnd,
  ) => `WITH expense_data AS (
    SELECT
      a."name" AS account_name,
      SUM(pr."amount") AS total_expense
    FROM "ProfitLossReport" pr
    JOIN "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
    WHERE
      pr."realmId" = '${realmId}'
      AND a."type" = 'Expense'
      AND (
        (pr."year" = ${fiscalYearStart.year} AND pr."month" >= ${fiscalYearStart.month})
        OR
        (pr."year" = ${fiscalYearEnd.year} AND pr."month" <= ${fiscalYearEnd.month})
        OR
        (pr."year" > ${fiscalYearStart.year} AND pr."year" < ${fiscalYearEnd.year})
      )
    GROUP BY a."name"
  ),
  expense_monthly AS (
    SELECT
      a."name" AS account_name,
      pr."year",
      pr."month",
      SUM(pr."amount") AS monthly_expense,
      -- Create a readable month-year format
      CASE
        WHEN pr."month" = 1 THEN 'Jan ' || pr."year"
        WHEN pr."month" = 2 THEN 'Feb ' || pr."year"
        WHEN pr."month" = 3 THEN 'Mar ' || pr."year"
        WHEN pr."month" = 4 THEN 'Apr ' || pr."year"
        WHEN pr."month" = 5 THEN 'May ' || pr."year"
        WHEN pr."month" = 6 THEN 'Jun ' || pr."year"
        WHEN pr."month" = 7 THEN 'Jul ' || pr."year"
        WHEN pr."month" = 8 THEN 'Aug ' || pr."year"
        WHEN pr."month" = 9 THEN 'Sep ' || pr."year"
        WHEN pr."month" = 10 THEN 'Oct ' || pr."year"
        WHEN pr."month" = 11 THEN 'Nov ' || pr."year"
        WHEN pr."month" = 12 THEN 'Dec ' || pr."year"
      END AS month_year
    FROM "ProfitLossReport" pr
    JOIN "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
    WHERE
      pr."realmId" = '${realmId}'
      AND a."type" = 'Expense'
      AND (
        (pr."year" = ${fiscalYearStart.year} AND pr."month" >= ${fiscalYearStart.month})
        OR
        (pr."year" = ${fiscalYearEnd.year} AND pr."month" <= ${fiscalYearEnd.month})
        OR
        (pr."year" > ${fiscalYearStart.year} AND pr."year" < ${fiscalYearEnd.year})
      )
    GROUP BY a."name", pr."year", pr."month"
  ),
  total_sum AS (
    SELECT SUM(total_expense) AS grand_total FROM expense_data
  ),
  top_accounts AS (
    SELECT *
    FROM expense_data
    ORDER BY total_expense DESC
    LIMIT 10
  ),
  other_total AS (
    SELECT
      'Other' AS account_name,
      SUM(ed.total_expense) AS total_expense
    FROM expense_data ed
    WHERE ed.account_name NOT IN (SELECT account_name FROM top_accounts)
  ),
  other_monthly AS (
    SELECT
      'Other' AS account_name,
      em."year",
      em."month",
      em.month_year,
      SUM(em.monthly_expense) AS monthly_expense
    FROM expense_monthly em
    WHERE em.account_name NOT IN (SELECT account_name FROM top_accounts)
    GROUP BY em."year", em."month", em.month_year
  ),
  combined_totals AS (
    SELECT * FROM top_accounts
    UNION ALL
    SELECT * FROM other_total
  ),
  combined_monthly AS (
    SELECT
      em.account_name,
      em."year",
      em."month",
      em.month_year,
      em.monthly_expense
    FROM expense_monthly em
    WHERE em.account_name IN (SELECT account_name FROM top_accounts)
    UNION ALL
    SELECT * FROM other_monthly
  )

-- Return all data without pivot for now (to avoid dynamic SQL complexity)
SELECT
  cm.account_name,
  ct.total_expense,
  ROUND((ct.total_expense / ts.grand_total) * 100, 2) AS percentage_of_total,
  cm."year",
  cm."month",
  cm.month_year,
  cm.monthly_expense
FROM combined_monthly cm
JOIN combined_totals ct ON cm.account_name = ct.account_name
CROSS JOIN total_sum ts
ORDER BY
  CASE WHEN cm.account_name = 'Other' THEN 1 ELSE 0 END,
  ct.total_expense DESC,
  cm."year",
  cm."month";`,

  //   expensesTopAccountsMonthly: (realmId, fiscalYearStart, fiscalYearEnd) => `WITH expense_data AS (
  //   SELECT
  //     a."name" AS account_name,
  //     pr."year",
  //     pr."month",
  //     SUM(pr."amount") AS monthly_expense
  //   FROM
  //     "ProfitLossReport" pr
  //   JOIN
  //     "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
  //   WHERE
  //     pr."realmId" = '${realmId}'
  //     AND a."type" = 'Expense'
  //     AND (
  //       (pr."year" = ${fiscalYearStart.year} AND pr."month" >= ${fiscalYearStart.month})
  //       OR
  //       (pr."year" = ${fiscalYearEnd.year} AND pr."month" <= ${fiscalYearEnd.month})
  //       OR
  //       (pr."year" > ${fiscalYearStart.year} AND pr."year" < ${fiscalYearEnd.year})
  //     )
  //   GROUP BY
  //     a."name", pr."year", pr."month"
  // ),
  // total_by_account AS (
  //   SELECT
  //     account_name,
  //     SUM(monthly_expense) AS total_expense
  //   FROM expense_data
  //   GROUP BY account_name
  // ),
  // top_accounts AS (
  //   SELECT account_name
  //   FROM total_by_account
  //   ORDER BY total_expense DESC
  //   LIMIT 10
  // ),
  // classified_expenses AS (
  //   SELECT
  //     CASE
  //       WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
  //       THEN ed.account_name
  //       ELSE 'Other'
  //     END AS account_name,
  //     ed.year,
  //     ed.month,
  //     SUM(ed.monthly_expense) AS monthly_expense
  //   FROM expense_data ed
  //   GROUP BY
  //     CASE
  //       WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
  //       THEN ed.account_name
  //       ELSE 'Other'
  //     END,
  //     ed.year, ed.month
  // )
  // -- Return normalized data without pivot for now
  // SELECT
  //   account_name,
  //   year,
  //   month,
  //   monthly_expense,
  //   SUM(monthly_expense) OVER (PARTITION BY account_name) AS total_expense
  // FROM classified_expenses
  // ORDER BY
  //   SUM(monthly_expense) OVER (PARTITION BY account_name) DESC,
  //   CASE WHEN account_name = 'Other' THEN 1 ELSE 0 END,
  //   year, month;`,

  expensesTopAccountsMonthly: (
    realmId,
    fiscalYearStart,
    fiscalYearEnd,
  ) => `WITH date_range AS (
  -- Generate all months from fiscal year start to fiscal year end
  SELECT 
    EXTRACT(YEAR FROM date_month)::INTEGER AS year,
    EXTRACT(MONTH FROM date_month)::INTEGER AS month
  FROM (
    SELECT generate_series(
      '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01'::date,
      '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01'::date,
      '1 month'::interval
    ) AS date_month
  ) months
),
expense_data AS (
  SELECT
    a."name" AS account_name,
    pr."year",
    pr."month",
    SUM(pr."amount") AS monthly_expense
  FROM
    "ProfitLossReport" pr
  JOIN
    "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
  WHERE
    pr."realmId" = '${realmId}'
    AND a."type" = 'Expense'
    AND (
      (pr."year" = ${fiscalYearStart.year} AND pr."month" >= ${fiscalYearStart.month})
      OR
      (pr."year" = ${fiscalYearEnd.year} AND pr."month" <= ${fiscalYearEnd.month})
      OR
      (pr."year" > ${fiscalYearStart.year} AND pr."year" < ${fiscalYearEnd.year})
    )
  GROUP BY
    a."name", pr."year", pr."month"
),
total_by_account AS (
  SELECT
    account_name,
    SUM(monthly_expense) AS total_expense
  FROM expense_data
  GROUP BY account_name
),
top_accounts AS (
  SELECT account_name
  FROM total_by_account
  ORDER BY total_expense DESC
  LIMIT 10
),
all_accounts AS (
  -- Ensure we always have at least 'Other' account, even if no expense data exists
  SELECT COALESCE(ta.account_name, 'Other') AS account_name
  FROM (
    SELECT account_name FROM top_accounts
    UNION ALL
    SELECT 'Other' AS account_name
  ) ta
  UNION
  -- Fallback: if no top accounts exist, ensure 'Other' is always present
  SELECT 'Other' AS account_name
),
account_month_combinations AS (
  -- Create all combinations of accounts and months - this ensures ALL 13 months are always present
  SELECT 
    aa.account_name,
    dr.year,
    dr.month
  FROM all_accounts aa
  CROSS JOIN date_range dr
),
classified_expenses AS (
  SELECT
    CASE
      WHEN ed.account_name IN (SELECT account_name FROM top_accounts WHERE account_name IS NOT NULL)
      THEN ed.account_name
      ELSE 'Other'
    END AS account_name,
    ed.year,
    ed.month,
    SUM(ed.monthly_expense) AS monthly_expense
  FROM expense_data ed
  GROUP BY
    CASE
      WHEN ed.account_name IN (SELECT account_name FROM top_accounts WHERE account_name IS NOT NULL)
      THEN ed.account_name
      ELSE 'Other'
    END,
    ed.year, ed.month
),
final_data AS (
  SELECT
    amc.account_name,
    amc.year,
    amc.month,
    COALESCE(ce.monthly_expense, 0) AS monthly_expense
  FROM account_month_combinations amc
  LEFT JOIN classified_expenses ce 
    ON amc.account_name = ce.account_name 
    AND amc.year = ce.year 
    AND amc.month = ce.month
)
-- Return normalized data with zeros for missing months - guaranteed to have all 13 months
SELECT
  account_name,
  year,
  month,
  monthly_expense,
  SUM(monthly_expense) OVER (PARTITION BY account_name) AS total_expense
FROM final_data
ORDER BY
  SUM(monthly_expense) OVER (PARTITION BY account_name) DESC,
  CASE WHEN account_name = 'Other' THEN 1 ELSE 0 END,
  year, month;`,

  // Days Sales Outstanding (DSO)
  daysSalesOutstanding: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH months AS (
    -- Generate all months from fiscal year start to fiscal year end
    SELECT 
      EXTRACT(YEAR FROM date_month)::INTEGER AS year,
      EXTRACT(MONTH FROM date_month)::INTEGER AS month
    FROM (
      SELECT generate_series(
        '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01'::date,
        '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01'::date,
        '1 month'::interval
      ) AS date_month
    ) month_series
  ),
  income AS (
    SELECT pl."year", pl."month", SUM(pl."amount") AS total_income
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
    WHERE a."type" = 'Income'
      AND pl."realmId" = '${realmId}'
    GROUP BY pl."year", pl."month"
  ),
  ar AS (
    SELECT "year", "month", SUM("total") AS accounts_receivable
    FROM "AccountReceivableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
    GROUP BY "year", "month"
  ),
  days_in_month AS (
    SELECT
      m.year,
      m.month,
      EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(m.year || '-' || m.month || '-01', 'YYYY-MM-DD'))
      + INTERVAL '1 month - 1 day'))::int AS days
    FROM months m
  )
  SELECT
    m.year,
    m.month,
    ROUND(
      CASE 
        WHEN COALESCE(i.total_income, 0) = 0 THEN 0
        ELSE COALESCE(ar.accounts_receivable, 0) / (i.total_income / d.days)
      END, 2
    ) AS days_sales_outstanding
  FROM months m
  LEFT JOIN income i ON m.year = i.year AND m.month = i.month
  LEFT JOIN ar ON m.year = ar.year AND m.month = ar.month
  LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
  ORDER BY m.year, m.month;
`,

  // Days Payables Outstanding (DPO)
  daysPayablesOutstanding: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH months AS (
    -- Generate all months from fiscal year start to fiscal year end
    SELECT 
      EXTRACT(YEAR FROM date_month)::INTEGER AS year,
      EXTRACT(MONTH FROM date_month)::INTEGER AS month
    FROM (
      SELECT generate_series(
        '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01'::date,
        '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01'::date,
        '1 month'::interval
      ) AS date_month
    ) month_series
  ),
  income AS (
    SELECT
      pl."year",
      pl."month",
      SUM(pl."amount") AS total_income
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
    WHERE a."type" = 'Income'
      AND pl."realmId" = '${realmId}'
    GROUP BY pl."year", pl."month"
  ),
  ap AS (
    SELECT "year", "month", SUM("total") AS accounts_payable
    FROM "AccountPayableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
    GROUP BY "year", "month"
  ),
  days_in_month AS (
    SELECT
      m.year,
      m.month,
      EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(m.year || '-' || m.month || '-01', 'YYYY-MM-DD'))
      + INTERVAL '1 month - 1 day'))::int AS days
    FROM months m
  )
  SELECT
    m.year,
    m.month,
    ROUND(
      CASE 
        WHEN COALESCE(i.total_income, 0) = 0 THEN 0
        ELSE COALESCE(ap.accounts_payable, 0) / (i.total_income / d.days)
      END, 2
    ) AS days_payables_outstanding
  FROM months m
  LEFT JOIN income i ON m.year = i.year AND m.month = i.month
  LEFT JOIN ap ON m.year = ap.year AND m.month = ap.month
  LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
  ORDER BY m.year, m.month;
`,

  // Net Change in Cash
  netChangeInCash: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH month_series AS (
    SELECT EXTRACT(YEAR FROM d)::INT AS "year",
           EXTRACT(MONTH FROM d)::INT AS "month"
    FROM generate_series(
      DATE '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01',
      DATE '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01',
      INTERVAL '1 month'
    ) d
  ),
  monthly_cash AS (
    SELECT
      b."year",
      b."month",
      SUM(b."statementAmount") AS closing_balance
    FROM "BalanceSheetReport" b
    JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
    WHERE a."type" IN ('Bank', 'Cash')
      AND b."realmId" = '${realmId}'
    GROUP BY b."year", b."month"
  ),
  cash_change AS (
    SELECT
      m."year",
      m."month",
      mc.closing_balance,
      LAG(mc.closing_balance) OVER (ORDER BY m."year", m."month") AS opening_balance
    FROM month_series m
    LEFT JOIN monthly_cash mc ON m."year" = mc."year" AND m."month" = mc."month"
  )
  SELECT
    "year",
    "month",
    CASE
      WHEN closing_balance IS NULL OR opening_balance IS NULL THEN '0.00'
      ELSE (closing_balance - opening_balance)::TEXT
    END AS net_change_in_cash
  FROM cash_change
  ORDER BY "year", "month";
`,

  // Quick Ratio
  quickRatio: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH month_series AS (
    SELECT
      EXTRACT(YEAR FROM d)::INT AS "year",
      EXTRACT(MONTH FROM d)::INT AS "month"
    FROM generate_series(
      DATE '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01',
      DATE '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01',
      INTERVAL '1 month'
    ) d
  ),
  balances AS (
    SELECT
      b."year",
      b."month",
      b."statementAmount" AS amount,
      a."type",
      a."accountSubTypeName",
      a."accountClassification"
    FROM "BalanceSheetReport" b
    JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
    WHERE b."realmId" = '${realmId}'
  ),
  monthly_aggregates AS (
    SELECT
      "year",
      "month",
      -- Quick assets: Cash + Short-term investments + Accounts receivable
      SUM(CASE 
        WHEN "type" IN ('Bank', 'Cash') OR "accountSubTypeName" IN ('Cash', 'Bank', 'CashEquivalents') 
        THEN amount 
        ELSE 0 
      END) AS cash_and_equivalents,
      SUM(CASE 
        WHEN "type" = 'Accounts Receivable' OR "accountSubTypeName" = 'AccountsReceivable' 
        THEN amount 
        ELSE 0 
      END) AS accounts_receivable,
      SUM(CASE 
        WHEN "accountSubTypeName" IN ('ShortTermInvestments', 'MarketableSecurities') 
        THEN amount 
        ELSE 0 
      END) AS short_term_investments,
      -- Current liabilities
      SUM(CASE 
        WHEN "accountClassification" = 'Liability' 
        AND ("accountSubTypeName" IN ('CreditCard', 'AccountsPayable', 'OtherCurrentLiabilities', 'ShortTermLiability', 'AccruedLiabilities') 
             OR "type" LIKE '%Current%')
        THEN ABS(amount)
        ELSE 0 
      END) AS current_liabilities
    FROM balances
    GROUP BY "year", "month"
  ),
  full_view AS (
    SELECT
      m."year",
      m."month",
      COALESCE(ma.cash_and_equivalents, 0) AS cash_and_equivalents,
      COALESCE(ma.accounts_receivable, 0) AS accounts_receivable,
      COALESCE(ma.short_term_investments, 0) AS short_term_investments,
      COALESCE(ma.current_liabilities, 0) AS current_liabilities,
      -- Calculate total quick assets
      (COALESCE(ma.cash_and_equivalents, 0) + 
       COALESCE(ma.accounts_receivable, 0) + 
       COALESCE(ma.short_term_investments, 0)) AS quick_assets
    FROM month_series m
    LEFT JOIN monthly_aggregates ma ON m."year" = ma."year" AND m."month" = ma."month"
  )
  SELECT
    "year",
    "month",
    cash_and_equivalents,
    accounts_receivable,
    short_term_investments,
    quick_assets,
    current_liabilities,
    CASE
      WHEN current_liabilities = 0 OR current_liabilities IS NULL THEN 
        CASE 
          WHEN quick_assets > 0 THEN '∞' 
          ELSE '0.00' 
        END
      ELSE ROUND((quick_assets / NULLIF(current_liabilities, 0))::NUMERIC, 2)::TEXT
    END AS quick_ratio
  FROM full_view
  ORDER BY "year", "month";
`,

  // ROA and ROE
  roaAndRoe: (realmId, fiscalYearStart, fiscalYearEnd) => `
WITH pnl AS (
  SELECT
    pl."year",
    pl."month",
    SUM(
      CASE
        WHEN a."type" = 'Income' THEN pl."amount"
        WHEN a."type" = 'Cost of Goods Sold' THEN -pl."amount"
        WHEN a."type" IN ('Expense', 'Other Expense') THEN -pl."amount"
        ELSE 0
      END
    ) AS net_profit
  FROM "ProfitLossReport" pl
  JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
  WHERE pl."realmId" = '${realmId}'
    AND (
      (pl."year" = ${fiscalYearStart.year} AND pl."month" >= ${fiscalYearStart.month})
      OR
      (pl."year" = ${fiscalYearEnd.year} AND pl."month" <= ${fiscalYearEnd.month})
      OR
      (pl."year" > ${fiscalYearStart.year} AND pl."year" < ${fiscalYearEnd.year})
    )
  GROUP BY pl."year", pl."month"
),
bs AS (
  SELECT
    bs."year",
    bs."month",
    SUM(CASE WHEN a."accountClassification" = 'Asset' THEN bs."statementAmount" ELSE 0 END) AS total_assets,
    SUM(CASE WHEN a."accountClassification" = 'Equity' THEN bs."statementAmount" ELSE 0 END) AS total_equity
  FROM "BalanceSheetReport" bs
  JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId"
  WHERE bs."realmId" = '${realmId}'
    AND (
      (bs."year" = ${fiscalYearStart.year} AND bs."month" >= ${fiscalYearStart.month})
      OR
      (bs."year" = ${fiscalYearEnd.year} AND bs."month" <= ${fiscalYearEnd.month})
      OR
      (bs."year" > ${fiscalYearStart.year} AND bs."year" < ${fiscalYearEnd.year})
    )
  GROUP BY bs."year", bs."month"
)
SELECT
  pnl."year",
  pnl."month",
  ROUND(CASE WHEN bs.total_assets <> 0 THEN pnl.net_profit * 100.0 / bs.total_assets ELSE NULL END, 2) AS roa,
  ROUND(CASE WHEN bs.total_equity <> 0 THEN pnl.net_profit * 100.0 / bs.total_equity ELSE NULL END, 2) AS roe
FROM pnl
LEFT JOIN bs ON pnl."year" = bs."year" AND pnl."month" = bs."month"
WHERE
  (bs.total_assets IS NOT NULL AND bs.total_assets <> 0)
  OR (bs.total_equity IS NOT NULL AND bs.total_equity <> 0)
ORDER BY pnl."year", pnl."month";
`,

  // Cash Conversion Cycle (CCC)
  cashConversionCycle: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH date_range AS (
    -- Generate all months from fiscal year start to fiscal year end
    SELECT 
      EXTRACT(YEAR FROM date_month)::INTEGER AS year,
      EXTRACT(MONTH FROM date_month)::INTEGER AS month
    FROM (
      SELECT generate_series(
        '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01'::date,
        '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01'::date,
        '1 month'::interval
      ) AS date_month
    ) months
  ),
  ar AS (
    SELECT "year", "month", COALESCE(SUM("total"), 0) AS ar_balance
    FROM "AccountReceivableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
      AND (
        ("year" = ${fiscalYearStart.year} AND "month" >= ${fiscalYearStart.month})
        OR
        ("year" = ${fiscalYearEnd.year} AND "month" <= ${fiscalYearEnd.month})
        OR
        ("year" > ${fiscalYearStart.year} AND "year" < ${fiscalYearEnd.year})
      )
    GROUP BY "year", "month"
  ),
  ap AS (
    SELECT "year", "month", COALESCE(SUM("total"), 0) AS ap_balance
    FROM "AccountPayableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
      AND (
        ("year" = ${fiscalYearStart.year} AND "month" >= ${fiscalYearStart.month})
        OR
        ("year" = ${fiscalYearEnd.year} AND "month" <= ${fiscalYearEnd.month})
        OR
        ("year" > ${fiscalYearStart.year} AND "year" < ${fiscalYearEnd.year})
      )
    GROUP BY "year", "month"
  ),
  inventory AS (
    SELECT bs."year", bs."month", COALESCE(SUM(bs."statementAmount"), 0) AS inventory_balance
    FROM "BalanceSheetReport" bs
    JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId" AND bs."userId" = a."userId"
    WHERE bs."realmId" = '${realmId}'
      AND a."type" = 'Asset'
      AND a."name" ILIKE '%inventory%'
      AND (
        ("year" = ${fiscalYearStart.year} AND "month" >= ${fiscalYearStart.month})
        OR
        ("year" = ${fiscalYearEnd.year} AND "month" <= ${fiscalYearEnd.month})
        OR
        ("year" > ${fiscalYearStart.year} AND "year" < ${fiscalYearEnd.year})
      )
    GROUP BY bs."year", bs."month"
  ),
  revenue AS (
    SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS income
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
    WHERE pl."realmId" = '${realmId}'
      AND a."type" = 'Income'
      AND (
        ("year" = ${fiscalYearStart.year} AND "month" >= ${fiscalYearStart.month})
        OR
        ("year" = ${fiscalYearEnd.year} AND "month" <= ${fiscalYearEnd.month})
        OR
        ("year" > ${fiscalYearStart.year} AND "year" < ${fiscalYearEnd.year})
      )
    GROUP BY pl."year", pl."month"
  ),
  cogs AS (
    SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS cogs
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
    WHERE pl."realmId" = '${realmId}'
      AND a."type" = 'Cost of Goods Sold'
      AND (
        ("year" = ${fiscalYearStart.year} AND "month" >= ${fiscalYearStart.month})
        OR
        ("year" = ${fiscalYearEnd.year} AND "month" <= ${fiscalYearEnd.month})
        OR
        ("year" > ${fiscalYearStart.year} AND "year" < ${fiscalYearEnd.year})
      )
    GROUP BY pl."year", pl."month"
  )
  SELECT
    dr.year,
    dr.month,
    COALESCE(re.income, 0) AS income,
    COALESCE(ar.ar_balance, 0) AS ar_balance,
    COALESCE(co.cogs, 0) AS cogs,
    COALESCE(inv.inventory_balance, 0) AS inventory_balance,
    COALESCE(ap.ap_balance, 0) AS ap_balance,
    ROUND(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (COALESCE(ar.ar_balance, 0) / re.income) * 30 ELSE 0 END, 2) AS dso,
    ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(inv.inventory_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dio,
    ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(ap.ap_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dpo,
    ROUND(
      COALESCE(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (ar.ar_balance / re.income) * 30 ELSE 0 END, 0) +
      COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (inv.inventory_balance / co.cogs) * 30 ELSE 0 END, 0) -
      COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (ap.ap_balance / co.cogs) * 30 ELSE 0 END, 0),
      2
    ) AS ccc
  FROM date_range dr
  LEFT JOIN revenue re ON dr.year = re."year" AND dr.month = re."month"
  LEFT JOIN cogs co ON dr.year = co."year" AND dr.month = co."month"
  LEFT JOIN ar ON dr.year = ar."year" AND dr.month = ar."month"
  LEFT JOIN ap ON dr.year = ap."year" AND dr.month = ap."month"
  LEFT JOIN inventory inv ON dr.year = inv."year" AND dr.month = inv."month"
  ORDER BY dr.year, dr.month;
`,

  // Fixed Asset Turnover (FAT)
  fixedAssetTurnover: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH revenue AS (
    SELECT
      pl."year",
      pl."month",
      COALESCE(SUM(pl."amount"), 0) AS total_revenue
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON
      pl."accountId" = a."accountId" AND
      pl."realmId" = a."realmId" AND
      pl."userId" = a."userId"
    WHERE pl."realmId" = '${realmId}'
      AND a."type" = 'Income'
      AND (
        (pl."year" = ${fiscalYearStart.year} AND pl."month" >= ${fiscalYearStart.month})
        OR
        (pl."year" = ${fiscalYearEnd.year} AND pl."month" <= ${fiscalYearEnd.month})
        OR
        (pl."year" > ${fiscalYearStart.year} AND pl."year" < ${fiscalYearEnd.year})
      )
    GROUP BY pl."year", pl."month"
  ),
  fixed_assets AS (
    SELECT
      bs."year",
      bs."month",
      COALESCE(SUM(bs."statementAmount"), 0) AS net_fixed_assets
    FROM "BalanceSheetReport" bs
    JOIN "Account" a ON
      bs."accountId" = a."accountId" AND
      bs."realmId" = a."realmId" AND
      bs."userId" = a."userId"
    WHERE bs."realmId" = '${realmId}'
      AND a."type" = 'Fixed Asset'
      AND (
        (bs."year" = ${fiscalYearStart.year} AND bs."month" >= ${fiscalYearStart.month})
        OR
        (bs."year" = ${fiscalYearEnd.year} AND bs."month" <= ${fiscalYearEnd.month})
        OR
        (bs."year" > ${fiscalYearStart.year} AND bs."year" < ${fiscalYearEnd.year})
      )
    GROUP BY bs."year", bs."month"
  ),
  combined AS (
    SELECT
      r."year",
      r."month",
      COALESCE(r.total_revenue, 0) AS total_revenue,
      COALESCE(fa.net_fixed_assets, 0) AS net_fixed_assets,
      COALESCE(LAG(fa.net_fixed_assets) OVER (ORDER BY r."year", r."month"), 0) AS prev_fixed_assets
    FROM revenue r
    LEFT JOIN fixed_assets fa ON r."year" = fa."year" AND r."month" = fa."month"
  )
  SELECT
    "year",
    "month",
    ROUND(
      CASE
        WHEN (net_fixed_assets + prev_fixed_assets) = 0 THEN 0
        ELSE total_revenue / ((net_fixed_assets + prev_fixed_assets) / 2)
      END,
      2
    ) AS fat
  FROM combined
  ORDER BY "year", "month"; 
`,

  // Months of Cash
  monthsOfCash: (realmId, fiscalYearStart, fiscalYearEnd) => `
  WITH months AS (
    SELECT
      EXTRACT(YEAR FROM d)::INT AS "year",
      EXTRACT(MONTH FROM d)::INT AS "month",
      d::DATE AS ref_date,
      EXTRACT(DAY FROM (DATE_TRUNC('month', d) + INTERVAL '1 month - 1 day'))::INT AS days_in_month
    FROM generate_series(
      DATE '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01',
      DATE '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01',
      INTERVAL '1 month'
    ) d
  ),
  
  -- Cash + A/R from Balance Sheet
  cash_ar AS (
    SELECT
      bs."year",
      bs."month",
      SUM(CASE WHEN a."type" = 'Bank' THEN bs."statementAmount" ELSE 0 END) AS cash,
      SUM(CASE WHEN a."type" = 'Accounts Receivable' THEN bs."statementAmount" ELSE 0 END) AS ar
    FROM "BalanceSheetReport" bs
    JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId"
    WHERE bs."realmId" = '${realmId}'
    GROUP BY bs."year", bs."month"
  ),
  
  -- Monthly COGS + Expense from P&L
  monthly_expense AS (
    SELECT
      pl."year",
      pl."month",
      SUM(CASE WHEN a."type" IN ('Expense', 'Cost of Goods Sold') THEN pl."amount" ELSE 0 END) AS monthly_expense
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY pl."year", pl."month"
  ),
  
  -- Flatten for rolling average
  pnl_with_date AS (
    SELECT
      MAKE_DATE(me."year", me."month", 1) AS period,
      me.monthly_expense
    FROM monthly_expense me
  ),
  
  -- Trailing 12-month average operating expense
  avg_expense_12mo AS (
    SELECT
      m."year",
      m."month",
      ROUND(AVG(p.monthly_expense)::NUMERIC, 2) AS avg_monthly_operating_expense
    FROM months m
    JOIN pnl_with_date p ON p.period BETWEEN (m.ref_date - INTERVAL '11 months') AND m.ref_date
    GROUP BY m."year", m."month"
  )
  
  -- Final result
  SELECT
    m."year",
    m."month",
    ROUND(COALESCE(ca.cash, 0) + COALESCE(ca.ar, 0), 2) AS cash_plus_ar,
    ROUND(COALESCE(me.monthly_expense, 0), 2) AS monthly_expense,
    ae.avg_monthly_operating_expense,
    m.days_in_month,
    ROUND(
      CASE
        WHEN ae.avg_monthly_operating_expense = 0 THEN 0
        ELSE (COALESCE(ca.cash, 0) + COALESCE(ca.ar, 0)) / ae.avg_monthly_operating_expense
      END,
      2
    ) AS months_cash
  FROM months m
  LEFT JOIN cash_ar ca ON m."year" = ca."year" AND m."month" = ca."month"
  LEFT JOIN monthly_expense me ON m."year" = me."year" AND m."month" = me."month"
  LEFT JOIN avg_expense_12mo ae ON m."year" = ae."year" AND m."month" = ae."month"
  ORDER BY m."year", m."month";
`,

  // YTD Total Income (as requested in your example)
  ytdTotalIncome: (realmId, fiscalYearStart, fiscalYearEnd) => `
  SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_Income"
  FROM "ProfitLossReport" pl
  JOIN "Account" a
  ON pl."accountId" = a."accountId"
  AND pl."realmId" = a."realmId"
  WHERE a."type" = 'Income'
  AND pl."realmId" = '${realmId}'
  AND (
    (pl."year" = ${fiscalYearStart.year} AND pl."month" >= ${fiscalYearStart.month})
    OR
    (pl."year" = ${fiscalYearEnd.year} AND pl."month" <= ${fiscalYearEnd.month})
    OR
    (pl."year" > ${fiscalYearStart.year} AND pl."year" < ${fiscalYearEnd.year})
  );
`,

  // 13 month trailing P&L by accounts
  profitAndLossMonthsTrailing: (realmId, fiscalYearStart, fiscalYearEnd) => {
    // Generate dynamic month ranges for the 13-month fiscal year
    const months = [];
    let currentYear = fiscalYearStart.year;
    let currentMonth = fiscalYearStart.month;

    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    while (
      currentYear < fiscalYearEnd.year ||
      (currentYear === fiscalYearEnd.year &&
        currentMonth <= fiscalYearEnd.month)
    ) {
      const monthName = monthNames[currentMonth - 1];
      const yearShort = currentYear.toString().slice(-2);
      const label = `${monthName} ${yearShort}`;

      months.push(`(${currentYear}, ${currentMonth}, '${label}')`);

      currentMonth++;
      if (currentMonth > 12) {
        currentMonth = 1;
        currentYear++;
      }
    }

    // Generate dynamic pivot columns
    const pivotColumns = [];
    const typeTotalColumns = [];

    currentYear = fiscalYearStart.year;
    currentMonth = fiscalYearStart.month;

    while (
      currentYear < fiscalYearEnd.year ||
      (currentYear === fiscalYearEnd.year &&
        currentMonth <= fiscalYearEnd.month)
    ) {
      const monthName = monthNames[currentMonth - 1];
      const yearShort = currentYear.toString().slice(-2);
      const label = `${monthName} ${yearShort}`;

      // For pivoted CTE
      pivotColumns.push(
        `SUM(CASE WHEN month_label = '${label}' THEN amount ELSE 0 END) AS "${label}"`,
      );

      // For type_totals CTE
      typeTotalColumns.push(`SUM("${label}") AS "${label}"`);

      currentMonth++;
      if (currentMonth > 12) {
        currentMonth = 1;
        currentYear++;
      }
    }

    return `WITH months AS (
    SELECT * FROM (
      VALUES
        ${months.join(', ')}
    ) AS m("year", "month", "label")
  ),
  all_accounts AS (
    SELECT
      "accountId",
      "name" AS account_name,
      "type" AS account_type
    FROM public."Account"
    WHERE "realmId" = '${realmId}'
  ),
  expanded_grid AS (
    SELECT
      aa."accountId",
      aa.account_name,
      aa.account_type,
      m."year",
      m."month",
      m."label"
    FROM all_accounts aa
    CROSS JOIN months m
  ),
  joined_data AS (
    SELECT
      eg.account_type,
      eg.account_name,
      eg."label" AS month_label,
      COALESCE(SUM(pl."amount"), 0) AS amount
    FROM expanded_grid eg
    LEFT JOIN "ProfitLossReport" pl
      ON pl."accountId"::text = eg."accountId"
      AND pl."realmId" = '${realmId}'
      AND pl."year" = eg."year"
      AND pl."month" = eg."month"
    GROUP BY eg.account_type, eg.account_name, eg."label"
  ),
  pivoted AS (
    SELECT
      account_type,
      account_name,
      ${pivotColumns.join(',\n      ')}
    FROM joined_data
    GROUP BY account_type, account_name
  ),
  type_totals AS (
    SELECT
      account_type,
      'Total ' || account_type AS account_name,
      ${typeTotalColumns.join(',\n      ')}
    FROM pivoted
    GROUP BY account_type
  ),
  combined AS (
    SELECT * FROM pivoted
    UNION ALL
    SELECT * FROM type_totals
  )
  SELECT *
  FROM combined
  ORDER BY
    account_type,
    CASE WHEN account_name LIKE 'Total %' THEN 1 ELSE 0 END,
    account_name;`;
  },
  // Balance Sheet
  balanceSheetTableData: (realmId, fiscalYearStart, fiscalYearEnd) => {
    // Generate dynamic month ranges for balance sheet (fiscal year start, prior fiscal year start, prior fiscal year end)
    const currentYear = fiscalYearStart.year;
    const currentMonth = fiscalYearStart.month;
    const priorYear = currentYear - 1;

    // Calculate prior fiscal year end (one month before current fiscal year start)
    let priorEndYear = currentYear;
    let priorEndMonth = currentMonth - 1;
    if (priorEndMonth < 1) {
      priorEndMonth = 12;
      priorEndYear = currentYear - 1;
    }

    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const currentLabel = `${monthNames[currentMonth - 1]} ${currentYear.toString().slice(-2)}`;
    const priorStartLabel = `${monthNames[currentMonth - 1]} ${priorYear.toString().slice(-2)}`;
    const priorEndLabel = `${monthNames[priorEndMonth - 1]} ${priorEndYear.toString().slice(-2)}`;
    console.log('priorYear: ', priorYear);
    console.log('priorEndMonth: ', priorEndMonth);
    console.log('currentMonth: ', currentMonth);

    return `WITH months AS (
  SELECT * FROM (
    VALUES
      (${priorYear}, ${currentMonth}, '${priorStartLabel}'),
      (${priorEndYear}, ${priorEndMonth}, '${priorEndLabel}'),
      (${currentYear}, ${currentMonth}, '${currentLabel}')
  ) AS m("year", "month", "label")
),

all_accounts AS (

  SELECT

    "accountId",

    "name" AS account_name,

    "type" AS account_type,

    "accountClassification"

  FROM "Account"

  WHERE "realmId" = '${realmId}'

    AND "accountClassification" IN ('Asset', 'Liability', 'Equity')

),

expanded_grid AS (

  SELECT

    aa."accountId",

    aa.account_name,

    aa.account_type,

    aa."accountClassification",

    m."year",

    m."month",

    m."label"

  FROM all_accounts aa

  CROSS JOIN months m

),

joined_data AS (

  SELECT

    eg."accountClassification",

    eg.account_type,

    eg.account_name,

    eg."label" AS month_label,

    COALESCE(SUM(bs."statementAmount"), 0) AS amount

  FROM expanded_grid eg

  LEFT JOIN "BalanceSheetReport" bs

    ON bs."accountId" = eg."accountId"

    AND bs."realmId" = '${realmId}'

    AND bs."year" = eg."year"

    AND bs."month" = eg."month"

  GROUP BY eg."accountClassification", eg.account_type, eg.account_name, eg."label"

),

pivoted AS (

  SELECT

    "accountClassification",

    account_type,

    account_name,

    SUM(CASE WHEN month_label = '${currentLabel}' THEN amount ELSE 0 END) AS "${currentLabel.replace(' ', '_')}_Actuals",
    SUM(CASE WHEN month_label = '${priorStartLabel}' THEN amount ELSE 0 END) AS "${priorStartLabel.replace(' ', '_')}_Prior_Year",
    SUM(CASE WHEN month_label = '${priorEndLabel}' THEN amount ELSE 0 END) AS "${priorEndLabel.replace(' ', '_')}_Prior_Month"

  FROM joined_data

  GROUP BY "accountClassification", account_type, account_name

),

pivoted_with_variance AS (

  SELECT

    "accountClassification",

    account_type,

    account_name,

    "${currentLabel.replace(' ', '_')}_Actuals",
    "${priorStartLabel.replace(' ', '_')}_Prior_Year",
    ("${currentLabel.replace(' ', '_')}_Actuals" - "${priorStartLabel.replace(' ', '_')}_Prior_Year") AS "Variance_Prior_Year",
    "${priorEndLabel.replace(' ', '_')}_Prior_Month",
    ("${currentLabel.replace(' ', '_')}_Actuals" - "${priorEndLabel.replace(' ', '_')}_Prior_Month") AS "Variance_Prior_Month"

  FROM pivoted

),

account_type_totals AS (

  SELECT

    "accountClassification",

    account_type,

    'Total ' || account_type AS account_name,

    SUM("${currentLabel.replace(' ', '_')}_Actuals") AS "${currentLabel.replace(' ', '_')}_Actuals",
    SUM("${priorStartLabel.replace(' ', '_')}_Prior_Year") AS "${priorStartLabel.replace(' ', '_')}_Prior_Year",
    SUM("Variance_Prior_Year") AS "Variance_Prior_Year",
    SUM("${priorEndLabel.replace(' ', '_')}_Prior_Month") AS "${priorEndLabel.replace(' ', '_')}_Prior_Month",
    SUM("Variance_Prior_Month") AS "Variance_Prior_Month"

  FROM pivoted_with_variance

  GROUP BY "accountClassification", account_type

  -- Only include account type totals where account_type is different from accountClassification

  HAVING account_type != "accountClassification"

),

classification_totals AS (

  SELECT

    "accountClassification",

    'Total' AS account_type,

    'Total ' || "accountClassification" AS account_name,

    SUM("${currentLabel.replace(' ', '_')}_Actuals") AS "${currentLabel.replace(' ', '_')}_Actuals",
    SUM("${priorStartLabel.replace(' ', '_')}_Prior_Year") AS "${priorStartLabel.replace(' ', '_')}_Prior_Year",
    SUM("Variance_Prior_Year") AS "Variance_Prior_Year",
    SUM("${priorEndLabel.replace(' ', '_')}_Prior_Month") AS "${priorEndLabel.replace(' ', '_')}_Prior_Month",
    SUM("Variance_Prior_Month") AS "Variance_Prior_Month"

  FROM pivoted_with_variance

  GROUP BY "accountClassification"

),

combined AS (

  SELECT * FROM pivoted_with_variance

  UNION ALL

  SELECT * FROM account_type_totals

  UNION ALL

  SELECT * FROM classification_totals

)

SELECT 

  "accountClassification",

  account_type,

  account_name,

  "${currentLabel.replace(' ', '_')}_Actuals",
  "${priorStartLabel.replace(' ', '_')}_Prior_Year",
  "Variance_Prior_Year",
  "${priorEndLabel.replace(' ', '_')}_Prior_Month",
  "Variance_Prior_Month"

FROM combined

ORDER BY

  CASE 

    WHEN "accountClassification" = 'Asset' THEN 1

    WHEN "accountClassification" = 'Liability' THEN 2

    WHEN "accountClassification" = 'Equity' THEN 3

    ELSE 4

  END,

  "accountClassification",

  account_type,

  CASE 
    WHEN account_name LIKE 'Total %' AND account_name NOT LIKE 'Total Asset%' AND account_name NOT LIKE 'Total Liability%' AND account_name NOT LIKE 'Total Equity%' THEN 1
    WHEN account_name LIKE 'Total Asset%' OR account_name LIKE 'Total Liability%' OR account_name LIKE 'Total Equity%' THEN 2
    ELSE 0 
  END,
  account_name
`;
  },
  profitAndLossMonthsYTD: (realmId, fiscalYearStart, fiscalYearEnd) => {
    // Calculate current fiscal year start month and prior year same month
    const currentYear = fiscalYearStart.year;
    const currentMonth = fiscalYearStart.month;
    const priorYear = currentYear - 1;

    // Generate month labels
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const monthName = monthNames[currentMonth - 1];
    const currentLabel = `${monthName}_${currentYear.toString().slice(-2)}`;
    const priorLabel = `${monthName}_${priorYear.toString().slice(-2)}`;

    return `WITH all_accounts AS (

  SELECT

    "accountId",

    "name" AS account_name,

    "type" AS account_type,

    "accountClassification"

  FROM "Account"

  WHERE "realmId" = '${realmId}'

    AND "accountClassification" IN ('Revenue', 'Expense')

),

ytd_data AS (

  SELECT

    aa."accountId",

    aa.account_name,

    aa.account_type,

    aa."accountClassification",

    -- Year-to-date 2025 (Jan-Apr)

    COALESCE(SUM(CASE 

      WHEN pl."year" = 2025 AND pl."month" BETWEEN 1 AND 4 

      THEN pl."amount" 

      ELSE 0 

    END), 0) AS "${currentLabel}_YTD_Actuals",

    -- Year-to-date prior year
    COALESCE(SUM(CASE
      WHEN pl."year" = ${priorYear} AND pl."month" BETWEEN 1 AND ${currentMonth}

      THEN pl."amount" 

      ELSE 0
    END), 0) AS "${priorLabel}_YTD_Prior_Year"

  FROM all_accounts aa

  LEFT JOIN "ProfitLossReport" pl

    ON pl."accountId" = aa."accountId"

    AND pl."realmId" = '${realmId}'

    AND ((pl."year" = 2025 AND pl."month" BETWEEN 1 AND 4) 

      OR (pl."year" = 2024 AND pl."month" BETWEEN 1 AND 4))

  GROUP BY aa."accountId", aa.account_name, aa.account_type, aa."accountClassification"

),

total_income_calculation AS (

  SELECT

    SUM("${currentLabel}_YTD_Actuals") AS total_income_current,
    SUM("${priorLabel}_YTD_Prior_Year") AS total_income_prior

  FROM ytd_data

  WHERE "accountClassification" = 'Revenue'

),

ytd_with_percentages AS (

  SELECT

    yd."accountClassification",

    yd.account_type,

    yd.account_name,

    yd."${currentLabel}_YTD_Actuals",
    yd."${priorLabel}_YTD_Prior_Year",
    (yd."${currentLabel}_YTD_Actuals" - yd."${priorLabel}_YTD_Prior_Year") AS "Variance_Amount",

    -- Calculate percentage of income for 2025

    CASE 

      WHEN tic.total_income_current != 0 THEN
        ROUND((yd."${currentLabel}_YTD_Actuals" / tic.total_income_current * 100)::numeric, 2)
      ELSE 0
    END AS "${currentLabel}_Percent_of_Income",
    -- Calculate percentage of income for prior year
    CASE
      WHEN tic.total_income_prior != 0 THEN
        ROUND((yd."${priorLabel}_YTD_Prior_Year" / tic.total_income_prior * 100)::numeric, 2)
      ELSE 0
    END AS "${priorLabel}_Percent_of_Income",

    -- Calculate variance percentage

    CASE 

      WHEN yd."${priorLabel}_YTD_Prior_Year" != 0 THEN
        ROUND(((yd."${currentLabel}_YTD_Actuals" - yd."${priorLabel}_YTD_Prior_Year") / ABS(yd."${priorLabel}_YTD_Prior_Year") * 100)::numeric, 2)
      ELSE 0
    END AS "Variance_Percentage"

  FROM ytd_data yd

  CROSS JOIN total_income_calculation tic

),

account_type_totals AS (

  SELECT

    "accountClassification",

    account_type,

    'Total ' || account_type AS account_name,

    SUM("${currentLabel}_YTD_Actuals") AS "${currentLabel}_YTD_Actuals",
    SUM("${priorLabel}_YTD_Prior_Year") AS "${priorLabel}_YTD_Prior_Year",
    SUM("Variance_Amount") AS "Variance_Amount",
    -- Recalculate percentages for totals
    CASE
      WHEN (SELECT total_income_current FROM total_income_calculation) != 0 THEN
        ROUND((SUM("${currentLabel}_YTD_Actuals") / (SELECT total_income_current FROM total_income_calculation) * 100)::numeric, 2)
      ELSE 0
    END AS "${currentLabel}_Percent_of_Income",
    CASE
      WHEN (SELECT total_income_prior FROM total_income_calculation) != 0 THEN
        ROUND((SUM("${priorLabel}_YTD_Prior_Year") / (SELECT total_income_prior FROM total_income_calculation) * 100)::numeric, 2)
      ELSE 0
    END AS "${priorLabel}_Percent_of_Income",
    CASE
      WHEN SUM("${priorLabel}_YTD_Prior_Year") != 0 THEN
        ROUND((SUM("Variance_Amount") / ABS(SUM("${priorLabel}_YTD_Prior_Year")) * 100)::numeric, 2)
      ELSE 0
    END AS "Variance_Percentage"

  FROM ytd_with_percentages

  GROUP BY "accountClassification", account_type

),

classification_totals AS (

  SELECT

    "accountClassification",

    'Total' AS account_type,

    'Total ' || "accountClassification" AS account_name,

    SUM("${currentLabel}_YTD_Actuals") AS "${currentLabel}_YTD_Actuals",
    SUM("${priorLabel}_YTD_Prior_Year") AS "${priorLabel}_YTD_Prior_Year",
    SUM("Variance_Amount") AS "Variance_Amount",
    -- Recalculate percentages for classification totals
    CASE
      WHEN (SELECT total_income_current FROM total_income_calculation) != 0 THEN
        ROUND((SUM("${currentLabel}_YTD_Actuals") / (SELECT total_income_current FROM total_income_calculation) * 100)::numeric, 2)
      ELSE 0
    END AS "${currentLabel}_Percent_of_Income",
    CASE
      WHEN (SELECT total_income_prior FROM total_income_calculation) != 0 THEN
        ROUND((SUM("${priorLabel}_YTD_Prior_Year") / (SELECT total_income_prior FROM total_income_calculation) * 100)::numeric, 2)
      ELSE 0
    END AS "${priorLabel}_Percent_of_Income",
    CASE
      WHEN SUM("${priorLabel}_YTD_Prior_Year") != 0 THEN
        ROUND((SUM("Variance_Amount") / ABS(SUM("${priorLabel}_YTD_Prior_Year")) * 100)::numeric, 2)
      ELSE 0
    END AS "Variance_Percentage"

  FROM ytd_with_percentages

  GROUP BY "accountClassification"

),

combined AS (

  SELECT * FROM ytd_with_percentages

  UNION ALL

  SELECT * FROM account_type_totals

  UNION ALL

  SELECT * FROM classification_totals

)

SELECT 

  "accountClassification",

  account_type,

  account_name,

  "${currentLabel}_YTD_Actuals",
  "${currentLabel}_Percent_of_Income",
  "${priorLabel}_YTD_Prior_Year",
  "${priorLabel}_Percent_of_Income",
  "Variance_Amount",
  "Variance_Percentage"

FROM combined

ORDER BY

  CASE 

    WHEN "accountClassification" = 'Revenue' THEN 1

    WHEN "accountClassification" = 'Expense' THEN 2

    ELSE 3

  END,

  "accountClassification",

  account_type,

  CASE 

    WHEN account_name LIKE 'Total %' AND account_name NOT LIKE 'Total Revenue%' AND account_name NOT LIKE 'Total Expense%' THEN 1

    WHEN account_name LIKE 'Total Revenue%' OR account_name LIKE 'Total Expense%' THEN 2

    ELSE 0 

  END,

  account_name;
 `;
  },

  profitAndLossMonthly: (realmId, fiscalYearStart, fiscalYearEnd) => {
    // Generate dynamic month ranges - current year and prior year for comparison
    const currentYear = fiscalYearStart.year;
    const currentMonth = fiscalYearStart.month;
    const priorYear = currentYear - 1;

    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    const currentLabel = `${monthNames[currentMonth - 1]}_${currentYear.toString().slice(-2)}`;
    const priorLabel = `${monthNames[currentMonth - 1]}_${priorYear.toString().slice(-2)}`;

    return `WITH months AS (
  SELECT * FROM (
    VALUES
      (${priorYear}, ${currentMonth}, '${priorLabel}'),
      (${currentYear}, ${currentMonth}, '${currentLabel}')
  ) AS m("year", "month", "label")
),
all_accounts AS (
  SELECT
    "accountId",
    "name" AS account_name,
    "type" AS account_type
  FROM "Account"
  WHERE "realmId" = '${realmId}'
),
expanded_grid AS (
  SELECT
    aa."accountId",
    aa.account_name,
    aa.account_type,
    m."year",
    m."month",
    m."label"
  FROM all_accounts aa
  CROSS JOIN months m
),
joined_data AS (
  SELECT
    eg.account_type,
    eg.account_name,
    eg."label" AS month_label,
    COALESCE(SUM(pl."amount"), 0) AS amount
  FROM expanded_grid eg
  LEFT JOIN "ProfitLossReport" pl
    ON pl."accountId" = eg."accountId"
    AND pl."realmId" = '${realmId}'
    AND pl."year" = eg."year"
    AND pl."month" = eg."month"
  GROUP BY eg.account_type, eg.account_name, eg."label"
),
pivoted AS (
  SELECT
    account_type,
    account_name,
    SUM(CASE WHEN month_label = '${currentLabel}' THEN amount ELSE 0 END) AS "${currentLabel}_Actuals",
    SUM(CASE WHEN month_label = '${priorLabel}' THEN amount ELSE 0 END) AS "${priorLabel}_Prior_Year"
  FROM joined_data
  GROUP BY account_type, account_name
),
total_revenue_calc AS (
  SELECT
    SUM(CASE WHEN month_label = '${currentLabel}' THEN amount ELSE 0 END) AS total_revenue_current,
    SUM(CASE WHEN month_label = '${priorLabel}' THEN amount ELSE 0 END) AS total_revenue_prior
  FROM joined_data
  WHERE account_type LIKE '%Income%' 
     OR account_type LIKE '%Revenue%'
     OR account_type LIKE '%Sales%'
),
pivoted_with_calculations AS (
  SELECT
    p.account_type,
    p.account_name,
    p."${currentLabel}_Actuals",
    -- Calculate % of Income for current year
    CASE 
      WHEN trc.total_revenue_current != 0 THEN 
        ROUND((p."${currentLabel}_Actuals" / trc.total_revenue_current * 100)::numeric, 0)
      ELSE 0 
    END AS "${currentLabel}_Percent_of_Income",
    p."${priorLabel}_Prior_Year",
    -- Calculate % of Income for prior year
    CASE 
      WHEN trc.total_revenue_prior != 0 THEN 
        ROUND((p."${priorLabel}_Prior_Year" / trc.total_revenue_prior * 100)::numeric, 0)
      ELSE 0 
    END AS "${priorLabel}_Percent_of_Income",
    -- Calculate Variance Amount
    (p."${currentLabel}_Actuals" - p."${priorLabel}_Prior_Year") AS "Variance_Amount",
    -- Calculate Variance Percentage
    CASE 
      WHEN p."${priorLabel}_Prior_Year" != 0 THEN 
        ROUND(((p."${currentLabel}_Actuals" - p."${priorLabel}_Prior_Year") / ABS(p."${priorLabel}_Prior_Year") * 100)::numeric, 0)
      ELSE 
        CASE WHEN p."${currentLabel}_Actuals" != 0 THEN 100 ELSE 0 END
    END AS "Variance_Percentage"
  FROM pivoted p
  CROSS JOIN total_revenue_calc trc
),
type_totals AS (
  SELECT
    account_type,
    'Total ' || account_type AS account_name,
    SUM("${currentLabel}_Actuals") AS "${currentLabel}_Actuals",
    -- Calculate % of Income for totals current year
    CASE 
      WHEN (SELECT total_revenue_current FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("${currentLabel}_Actuals") / (SELECT total_revenue_current FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "${currentLabel}_Percent_of_Income",
    SUM("${priorLabel}_Prior_Year") AS "${priorLabel}_Prior_Year",
    -- Calculate % of Income for totals prior year
    CASE 
      WHEN (SELECT total_revenue_prior FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("${priorLabel}_Prior_Year") / (SELECT total_revenue_prior FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "${priorLabel}_Percent_of_Income",
    SUM("Variance_Amount") AS "Variance_Amount",
    CASE 
      WHEN SUM("${priorLabel}_Prior_Year") != 0 THEN 
        ROUND((SUM("Variance_Amount") / ABS(SUM("${priorLabel}_Prior_Year")) * 100)::numeric, 0)
      ELSE 
        CASE WHEN SUM("${currentLabel}_Actuals") != 0 THEN 100 ELSE 0 END
    END AS "Variance_Percentage"
  FROM pivoted_with_calculations
  GROUP BY account_type
),
classification_totals AS (
  SELECT
    'Total' AS account_type,
    'Total Income' AS account_name,
    SUM("${currentLabel}_Actuals") AS "${currentLabel}_Actuals",
    CASE 
      WHEN (SELECT total_revenue_current FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("${currentLabel}_Actuals") / (SELECT total_revenue_current FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "${currentLabel}_Percent_of_Income",
    SUM("${priorLabel}_Prior_Year") AS "${priorLabel}_Prior_Year",
    CASE 
      WHEN (SELECT total_revenue_prior FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("${priorLabel}_Prior_Year") / (SELECT total_revenue_prior FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "${priorLabel}_Percent_of_Income",
    SUM("Variance_Amount") AS "Variance_Amount",
    CASE 
      WHEN SUM("${priorLabel}_Prior_Year") != 0 THEN 
        ROUND((SUM("Variance_Amount") / ABS(SUM("${priorLabel}_Prior_Year")) * 100)::numeric, 0)
      ELSE 
        CASE WHEN SUM("${currentLabel}_Actuals") != 0 THEN 100 ELSE 0 END
    END AS "Variance_Percentage"
  FROM pivoted_with_calculations
  WHERE account_type LIKE '%Income%' 
     OR account_type LIKE '%Revenue%'
     OR account_type LIKE '%Sales%'
),
combined AS (
  SELECT * FROM pivoted_with_calculations
  UNION ALL
  SELECT * FROM type_totals
  UNION ALL
  SELECT * FROM classification_totals
)
SELECT 
  account_type,
  account_name,
  "${currentLabel}_Actuals",
  "${currentLabel}_Percent_of_Income",
  "${priorLabel}_Prior_Year",
  "${priorLabel}_Percent_of_Income",
  "Variance_Amount",
  "Variance_Percentage"
FROM combined
ORDER BY
  account_type,
  CASE WHEN account_name LIKE 'Total %' THEN 1 ELSE 0 END,
  account_name;`;
  },

  wagesRevenueMonthWise: (realmId, fiscalYearStart, fiscalYearEnd) => `
WITH date_range AS (
  SELECT
    EXTRACT(YEAR FROM d)::INT AS year,
    EXTRACT(MONTH FROM d)::INT AS month
  FROM generate_series(
    DATE '${fiscalYearStart.year}-${fiscalYearStart.month.toString().padStart(2, '0')}-01',
    DATE '${fiscalYearEnd.year}-${fiscalYearEnd.month.toString().padStart(2, '0')}-01',
    INTERVAL '1 month'
  ) AS d
),
wage_accounts AS (
  SELECT "accountId", "userId", "realmId"
  FROM "Account"
  WHERE 
    "accountSubTypeName" ILIKE '%wage%' OR
    "name" ILIKE '%salary%' OR 
    "name" ILIKE '%wages%' OR 
    "name" ILIKE '%payroll%'
),
income_vs_wages AS (
  SELECT 
    dr.year,
    dr.month,
    COALESCE(SUM(CASE WHEN a."accountId" IN (
      SELECT "accountId" FROM wage_accounts
    ) THEN pl."amount" ELSE 0 END), 0) AS wages,
    COALESCE(SUM(CASE WHEN a."accountId" NOT IN (
      SELECT "accountId" FROM wage_accounts
    ) THEN pl."amount" ELSE 0 END), 0) AS income
  FROM date_range dr
  LEFT JOIN "ProfitLossReport" pl 
    ON pl."year" = dr.year AND pl."month" = dr.month AND pl."realmId" = '${realmId}'
  LEFT JOIN "Account" a 
    ON a."accountId" = pl."accountId" AND a."userId" = pl."userId" AND a."realmId" = pl."realmId"
  GROUP BY dr.year, dr.month
  ORDER BY dr.year, dr.month
)
SELECT * FROM income_vs_wages;

`,

  // daysInventoryOutstanding: (realmId, fiscalYearStart, fiscalYearEnd) => {
  //   // Generate dynamic month ranges for the 13-month fiscal year
  //   const months = [];
  //   let currentYear = fiscalYearStart.year;
  //   let currentMonth = fiscalYearStart.month;

  //   while (
  //     currentYear < fiscalYearEnd.year ||
  //     (currentYear === fiscalYearEnd.year &&
  //       currentMonth <= fiscalYearEnd.month)
  //   ) {
  //     months.push(`SELECT ${currentYear} AS year, ${currentMonth} AS month`);

  //     currentMonth++;
  //     if (currentMonth > 12) {
  //       currentMonth = 1;
  //       currentYear++;
  //     }
  //   }

  //   return `WITH months AS (
  //     ${months.join(' UNION ALL ')}
  //   ),
  //   inventory AS (
  //     SELECT
  //       b."year",
  //       b."month",
  //       MAX(b."statementAmount") AS inventory_balance
  //     FROM "BalanceSheetReport" b
  //     JOIN "Account" a ON a."accountId" = b."accountId"
  //     WHERE b."realmId" = '${realmId}'
  //       AND a."accountClassification" = 'Asset'
  //       AND (
  //         a."name" ILIKE '%inventory%' OR
  //         a."accountSubTypeName" ILIKE '%inventory%'
  //       )
  //     GROUP BY b."year", b."month"
  //   ),
  //   cogs AS (
  //     SELECT
  //       "year",
  //       "month",
  //       SUM("amount") AS total_cogs
  //     FROM (
  //       SELECT DISTINCT ON (p."year", p."month", p."accountId")
  //         p."year",
  //         p."month",
  //         p."accountId",
  //         p."amount"
  //       FROM "ProfitLossReport" p
  //       JOIN "Account" a ON a."accountId" = p."accountId"
  //       WHERE p."realmId" = '${realmId}'
  //         AND a."type" = 'Cost of Goods Sold'
  //     ) deduped
  //     GROUP BY "year", "month"
  //   ),
  //   days_in_month AS (
  //     SELECT
  //       m.year,
  //       m.month,
  //       EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(m.year, m.month, 1))
  //                         + INTERVAL '1 month - 1 day'))::int AS days
  //     FROM months m
  //   ),
  //   avg_inventory AS (
  //     SELECT
  //       curr.year,
  //       curr.month,
  //       (COALESCE(prev.inventory_balance, curr.inventory_balance) + curr.inventory_balance) / 2.0 AS average_inventory
  //     FROM inventory curr
  //     LEFT JOIN inventory prev
  //       ON (prev.year = curr.year AND prev.month = curr.month - 1)
  //       OR (prev.year = curr.year - 1 AND prev.month = 12 AND curr.month = 1)
  //   )
  //   SELECT
  //     m.year,
  //     m.month,
  //     ai.average_inventory,
  //     c.total_cogs,
  //     d.days,
  //     ROUND(
  //       CASE
  //         WHEN c.total_cogs IS NULL OR c.total_cogs = 0 THEN 0
  //         ELSE ai.average_inventory / (c.total_cogs / d.days)
  //       END, 2
  //     ) AS days_inventory_outstanding
  //   FROM months m
  //   LEFT JOIN avg_inventory ai ON ai.year = m.year AND ai.month = m.month
  //   LEFT JOIN cogs c ON c.year = m.year AND c.month = m.month
  //   LEFT JOIN days_in_month d ON d.year = m.year AND d.month = m.month
  //   ORDER BY year, month;`;
  // },
  
daysInventoryOutstanding: (realmId, fiscalYearStart, fiscalYearEnd) => {
  // Generate dynamic month ranges for the fiscal year period
  const months = [];
  let currentYear = fiscalYearStart.year;
  let currentMonth = fiscalYearStart.month;

  while (
    currentYear < fiscalYearEnd.year ||
    (currentYear === fiscalYearEnd.year && currentMonth <= fiscalYearEnd.month)
  ) {
    months.push(`SELECT ${currentYear} AS year, ${currentMonth} AS month`);
    currentMonth++;
    if (currentMonth > 12) {
      currentMonth = 1;
      currentYear++;
    }
  }

  return `
    WITH months AS (
      ${months.join(' UNION ALL ')}
    ),
    
    -- Get inventory balances from balance sheet (matching your original approach)
    inventory_raw AS (
      SELECT
        bs."year",
        bs."month",
        SUM(bs."statementAmount") AS inventory_balance
      FROM "BalanceSheetReport" bs
      JOIN "Account" a ON a."accountId" = bs."accountId"
      WHERE bs."realmId" = '${realmId}'
        AND a."accountClassification" = 'Asset'
        AND (
          a."name" ILIKE '%inventory%' OR
          a."accountSubTypeName" ILIKE '%inventory%'
        )
      GROUP BY bs."year", bs."month"
    ),
    
    -- Ensure all months have inventory data (fill with 0 if missing)
    inventory_complete AS (
      SELECT
        m.year,
        m.month,
        COALESCE(ir.inventory_balance, 0) AS inventory_balance
      FROM months m
      LEFT JOIN inventory_raw ir ON ir.year = m.year AND ir.month = m.month
    ),
    
    -- Get COGS from P&L (matching your original approach)
    cogs_raw AS (
      SELECT
        pl."year",
        pl."month",
        SUM(pl."amount") AS total_cogs
      FROM "ProfitLossReport" pl
      JOIN "Account" a ON a."accountId" = pl."accountId"
      WHERE pl."realmId" = '${realmId}'
        AND a."type" = 'Cost of Goods Sold'
      GROUP BY pl."year", pl."month"
    ),
    
    -- Ensure all months have COGS data
    cogs_complete AS (
      SELECT
        m.year,
        m.month,
        COALESCE(cr.total_cogs, 0) AS total_cogs
      FROM months m
      LEFT JOIN cogs_raw cr ON cr.year = m.year AND cr.month = m.month
    ),
    
    -- Calculate days in each month
    days_in_month AS (
      SELECT
        m.year,
        m.month,
        EXTRACT(DAY FROM (
          DATE_TRUNC('month', MAKE_DATE(m.year, m.month, 1)) + INTERVAL '1 month - 1 day'
        ))::int AS days
      FROM months m
    ),
    
    -- Calculate average inventory (current month + previous month) / 2
    avg_inventory AS (
      SELECT
        curr.year,
        curr.month,
        curr.inventory_balance AS current_inventory,
        LAG(curr.inventory_balance, 1, curr.inventory_balance) OVER (
          ORDER BY curr.year, curr.month
        ) AS previous_inventory,
        (curr.inventory_balance + LAG(curr.inventory_balance, 1, curr.inventory_balance) OVER (
          ORDER BY curr.year, curr.month
        )) / 2.0 AS average_inventory
      FROM inventory_complete curr
    )
    
    -- Final calculation
    SELECT
      m.year,
      m.month,
      ROUND(ai.current_inventory::numeric, 2) AS current_inventory,
      ROUND(ai.previous_inventory::numeric, 2) AS previous_inventory,
      ROUND(ai.average_inventory::numeric, 2) AS average_inventory,
      ROUND(c.total_cogs::numeric, 2) AS total_cogs,
      d.days,
      CASE
        WHEN c.total_cogs IS NULL OR c.total_cogs <= 0 THEN 0
        ELSE ROUND(
          (ai.average_inventory / NULLIF(c.total_cogs, 0) * d.days)::numeric, 
          2
        )
      END AS days_inventory_outstanding
    FROM months m
    LEFT JOIN avg_inventory ai ON ai.year = m.year AND ai.month = m.month
    LEFT JOIN cogs_complete c ON c.year = m.year AND c.month = m.month
    LEFT JOIN days_in_month d ON d.year = m.year AND d.month = m.month
    ORDER BY m.year, m.month;
  `;
},
};

// Helper function to execute a single query with realmId parameter

export async function executeQuery(queryFunc, realmId, queryName, fiscalYearStart, fiscalYearEnd) {
  try {
  

    const query = queryFunc(realmId, fiscalYearStart, fiscalYearEnd);
    const result = await prisma.$queryRawUnsafe(query);
    return {
      success: true,
      message: 'Data get successfully',
      data: result,
    };
  } catch (error) {
    console.error(`Error executing ${queryName}:`, error);
    return {
      success: false,
      error: error.message,
      data: [],
    };
  }
}
 
