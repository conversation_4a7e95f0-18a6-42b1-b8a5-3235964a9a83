import * as pdfService from '../services/pdfGeneration.service.js';
import { createErrorResponse } from '../utils/response.js';
import { HttpStatusCode } from '../enums/error.enum.js';

// export const generateCustomReportPDF = async (req, res) => {
//   try {
//     const { reportData, templateSettings } = req.body;

//     if (!reportData) {
//       return res.status(400).json(
//         createErrorResponse(
//           HttpStatusCode.BAD_REQUEST,
//           'Report data is required'
//         )
//       );
//     }

//     const result = await pdfService.generateCustomReportPDF(reportData, templateSettings);

//     if (result.success) {
//       res.status(200).json(result);
//     } else {
//       res.status(result.statusCode || 500).json(result);
//     }

//   } catch (error) {
//     console.error('PDF Controller Error:', error);
//     res.status(500).json(
//       createErrorResponse(
//         HttpStatusCode.INTERNAL_SERVER_ERROR,
//         'Failed to generate PDF',
//         error.message
//       )
//     );
//   }
// };

export const generatePDFFromHTML = async (req, res) => {
  try {
    const { htmlContent, options } = req.body;

    if (!htmlContent) {
      return res.status(400).json(
        createErrorResponse(
          HttpStatusCode.BAD_REQUEST,
          'HTML content is required'
        )
      );
    }

    const result = await pdfService.generateReportPDF(htmlContent, options);

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(result.statusCode || 500).json(result);
    }

  } catch (error) {
    console.error('PDF HTML Controller Error:', error);
    res.status(500).json(
      createErrorResponse(
        HttpStatusCode.INTERNAL_SERVER_ERROR,
        'Failed to generate PDF from HTML',
        error.message
      )
    );
  }
};